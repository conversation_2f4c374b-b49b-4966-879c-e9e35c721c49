"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/deposits/page",{

/***/ "(app-pages-browser)/./src/app/admin/deposits/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/admin/deposits/page.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminDepositsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/admin/AdminLayout */ \"(app-pages-browser)/./src/components/admin/AdminLayout.tsx\");\n/* harmony import */ var _lib_services_wallet__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/wallet */ \"(app-pages-browser)/./src/lib/services/wallet.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nfunction DepositActions(param) {\n    let { deposit, onApprove, onReject } = param;\n    _s();\n    const [showMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setShowMenu(!showMenu),\n                className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-1\",\n                    children: deposit.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    onApprove(deposit.id);\n                                    setShowMenu(false);\n                                },\n                                className: \"flex items-center w-full px-4 py-2 text-sm text-green-600 hover:bg-green-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Approve Deposit\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    onReject(deposit.id);\n                                    setShowMenu(false);\n                                },\n                                className: \"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Reject Deposit\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n_s(DepositActions, \"2FjIcsdimgVhm2IsUWodA2ftTZU=\");\n_c = DepositActions;\nfunction AdminDepositsPage() {\n    var _selectedDeposit_user;\n    _s1();\n    const [deposits, setDeposits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalDeposits, setTotalDeposits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const depositsPerPage = 20;\n    // Modal states\n    const [showRejectModal, setShowRejectModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showModifyModal, setShowModifyModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedDeposit, setSelectedDeposit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rejectNotes, setRejectNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [modifiedAmount, setModifiedAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchDeposits();\n    }, [\n        currentPage,\n        selectedStatus\n    ]);\n    const fetchDeposits = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const status = selectedStatus === \"all\" ? undefined : selectedStatus;\n            const { requests, total } = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_3__.WalletService.getAllDepositRequests(currentPage, depositsPerPage, status);\n            setDeposits(requests || []);\n            setTotalDeposits(total || 0);\n        } catch (err) {\n            console.error(\"Error fetching deposits:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to load deposits\");\n            setDeposits([]);\n            setTotalDeposits(0);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleApprove = async (depositId)=>{\n        if (!confirm(\"Are you sure you want to approve this deposit?\")) return;\n        try {\n            setActionLoading(true);\n            const { data: { user } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_5__.supabase.auth.getUser();\n            if (!user) throw new Error(\"Not authenticated\");\n            await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_3__.WalletService.approveDepositRequest(depositId, user.id);\n            await fetchDeposits();\n            alert(\"Deposit approved successfully!\");\n        } catch (err) {\n            alert(err instanceof Error ? err.message : \"Failed to approve deposit\");\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleReject = (depositId)=>{\n        const deposit = deposits.find((d)=>d.id === depositId);\n        if (!deposit) return;\n        setSelectedDeposit(deposit);\n        setShowRejectModal(true);\n    };\n    const handleModifyAmount = (depositId)=>{\n        const deposit = deposits.find((d)=>d.id === depositId);\n        if (!deposit) return;\n        setSelectedDeposit(deposit);\n        setModifiedAmount(deposit.amount.toString());\n        setShowModifyModal(true);\n    };\n    const confirmReject = async ()=>{\n        if (!selectedDeposit || !rejectNotes.trim()) return;\n        try {\n            setActionLoading(true);\n            const { data: { user } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_5__.supabase.auth.getUser();\n            if (!user) throw new Error(\"Not authenticated\");\n            await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_3__.WalletService.rejectDepositRequest(selectedDeposit.id, user.id, rejectNotes);\n            setShowRejectModal(false);\n            setSelectedDeposit(null);\n            setRejectNotes(\"\");\n            await fetchDeposits();\n            alert(\"Deposit rejected successfully!\");\n        } catch (err) {\n            alert(err instanceof Error ? err.message : \"Failed to reject deposit\");\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"approved\":\n                return \"text-green-600 bg-green-100\";\n            case \"pending\":\n                return \"text-yellow-600 bg-yellow-100\";\n            case \"rejected\":\n                return \"text-red-600 bg-red-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"approved\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 16\n                }, this);\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 16\n                }, this);\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const filteredDeposits = deposits.filter((deposit)=>{\n        var _deposit_user_full_name, _deposit_user, _deposit_user_email, _deposit_user1;\n        const matchesSearch = ((_deposit_user = deposit.user) === null || _deposit_user === void 0 ? void 0 : (_deposit_user_full_name = _deposit_user.full_name) === null || _deposit_user_full_name === void 0 ? void 0 : _deposit_user_full_name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_deposit_user1 = deposit.user) === null || _deposit_user1 === void 0 ? void 0 : (_deposit_user_email = _deposit_user1.email) === null || _deposit_user_email === void 0 ? void 0 : _deposit_user_email.toLowerCase().includes(searchTerm.toLowerCase())) || deposit.bank_name.toLowerCase().includes(searchTerm.toLowerCase()) || deposit.account_holder_name.toLowerCase().includes(searchTerm.toLowerCase());\n        return matchesSearch;\n    });\n    const totalPages = Math.ceil(totalDeposits / depositsPerPage);\n    const statusCounts = {\n        all: totalDeposits,\n        pending: deposits.filter((d)=>d.status === \"pending\").length,\n        approved: deposits.filter((d)=>d.status === \"approved\").length,\n        rejected: deposits.filter((d)=>d.status === \"rejected\").length\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Deposit Requests\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Review and manage user deposit requests\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            totalDeposits,\n                                            \" total requests\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                        children: Object.entries(statusCounts).map((param)=>{\n                            let [status, count] = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>setSelectedStatus(status),\n                                className: \"p-4 rounded-lg border cursor-pointer transition-colors \".concat(selectedStatus === status ? \"border-primary-blue bg-primary-blue/5\" : \"border-gray-200 hover:border-gray-300\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: count\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600 capitalize\",\n                                        children: status === \"all\" ? \"Total Requests\" : \"\".concat(status, \" Requests\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, status, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search by user, bank, or account holder...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: fetchDeposits,\n                                    className: \"px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90\",\n                                    children: \"Try Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full divide-y divide-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"User\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Amount\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Depositor & Reference\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Submitted\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-white divide-y divide-gray-200\",\n                                        children: filteredDeposits.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                colSpan: 6,\n                                                className: \"px-6 py-12 text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-12 w-12 text-gray-400 mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                            children: \"No deposit requests found\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500\",\n                                                            children: searchTerm ? \"Try adjusting your search terms.\" : \"No deposit requests have been submitted yet.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 21\n                                        }, this) : filteredDeposits.map((deposit)=>{\n                                            var _deposit_user, _deposit_user1;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-10 w-10 rounded-full bg-primary-blue/10 flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-primary-blue\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                    lineNumber: 329,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                            children: ((_deposit_user = deposit.user) === null || _deposit_user === void 0 ? void 0 : _deposit_user.full_name) || \"No name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                            lineNumber: 333,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: ((_deposit_user1 = deposit.user) === null || _deposit_user1 === void 0 ? void 0 : _deposit_user1.email) || \"No email\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                            lineNumber: 336,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                    lineNumber: 332,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-bold text-gray-900\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(deposit.amount)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-900\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        \"Depositor: \",\n                                                                        deposit.depositor_name || \"Not specified\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                deposit.reference_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-400 font-mono\",\n                                                                    children: [\n                                                                        \"Ref: \",\n                                                                        deposit.reference_number\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                deposit.deposit_slip_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                        href: deposit.deposit_slip_url,\n                                                                        target: \"_blank\",\n                                                                        rel: \"noopener noreferrer\",\n                                                                        className: \"inline-flex items-center text-xs text-blue-600 hover:text-blue-800\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"h-3 w-3 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                                lineNumber: 363,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            \"View Proof\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                        lineNumber: 357,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                    lineNumber: 356,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(getStatusColor(deposit.status)),\n                                                            children: [\n                                                                getStatusIcon(deposit.status),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-1 capitalize\",\n                                                                    children: deposit.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                    lineNumber: 373,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 text-sm text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                        lineNumber: 378,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    new Date(deposit.created_at).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: new Date(deposit.created_at).toLocaleTimeString()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DepositActions, {\n                                                            deposit: deposit,\n                                                            onApprove: handleApprove,\n                                                            onReject: handleReject\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, deposit.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, this),\n                    totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-700\",\n                                children: [\n                                    \"Showing \",\n                                    (currentPage - 1) * depositsPerPage + 1,\n                                    \" to \",\n                                    Math.min(currentPage * depositsPerPage, totalDeposits),\n                                    \" of \",\n                                    totalDeposits,\n                                    \" requests\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setCurrentPage((prev)=>Math.max(prev - 1, 1)),\n                                        disabled: currentPage === 1,\n                                        className: \"px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\",\n                                        children: \"Previous\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, this),\n                                    Array.from({\n                                        length: Math.min(5, totalPages)\n                                    }, (_, i)=>{\n                                        const page = i + Math.max(1, currentPage - 2);\n                                        return page <= totalPages ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentPage(page),\n                                            className: \"px-3 py-2 border rounded-lg \".concat(currentPage === page ? \"bg-primary-blue text-white border-primary-blue\" : \"border-gray-300 hover:bg-gray-50\"),\n                                            children: page\n                                        }, page, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 19\n                                        }, this) : null;\n                                    }),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setCurrentPage((prev)=>Math.min(prev + 1, totalPages)),\n                                        disabled: currentPage === totalPages,\n                                        className: \"px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\",\n                                        children: \"Next\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                lineNumber: 221,\n                columnNumber: 7\n            }, this),\n            showRejectModal && selectedDeposit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Reject Deposit Request\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 449,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mb-2\",\n                                children: [\n                                    \"You are about to reject a deposit request for\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(selectedDeposit.amount)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" \",\n                                    \"from \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: (_selectedDeposit_user = selectedDeposit.user) === null || _selectedDeposit_user === void 0 ? void 0 : _selectedDeposit_user.full_name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 22\n                                    }, this),\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Reason for rejection *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    required: true,\n                                    value: rejectNotes,\n                                    onChange: (e)=>setRejectNotes(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                    rows: 4,\n                                    placeholder: \"Please provide a reason for rejecting this deposit request...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                    lineNumber: 461,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>{\n                                        setShowRejectModal(false);\n                                        setSelectedDeposit(null);\n                                        setRejectNotes(\"\");\n                                    },\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: confirmReject,\n                                    disabled: actionLoading || !rejectNotes.trim(),\n                                    className: \"flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50\",\n                                    children: actionLoading ? \"Rejecting...\" : \"Reject Deposit\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 448,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                lineNumber: 447,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n        lineNumber: 220,\n        columnNumber: 5\n    }, this);\n}\n_s1(AdminDepositsPage, \"IT94xPlRLdb2MkMAHMGQYy02XII=\");\n_c1 = AdminDepositsPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"DepositActions\");\n$RefreshReg$(_c1, \"AdminDepositsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/deposits/page.tsx\n"));

/***/ })

});