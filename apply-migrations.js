const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials in .env.local')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function executeMigration(migrationFile) {
  console.log(`\n🔄 Applying migration: ${migrationFile}`)

  try {
    const migrationPath = path.join('supabase', 'migrations', migrationFile)
    const sql = fs.readFileSync(migrationPath, 'utf8')

    console.log(`Executing SQL from ${migrationFile}:`)
    console.log('---')
    console.log(sql)
    console.log('---')

    // Execute the entire SQL as one query
    const { data, error } = await supabase
      .from('_dummy') // This won't work, we need to use raw SQL
      .select('*')

    // Let's use a different approach - execute raw SQL
    const { data: result, error: sqlError } = await supabase.rpc('exec_raw_sql', {
      sql: sql
    })

    if (sqlError) {
      console.error(`❌ Error executing migration: ${sqlError.message}`)
    } else {
      console.log('✅ Migration executed successfully')
    }

  } catch (error) {
    console.error(`❌ Error applying migration ${migrationFile}:`, error.message)
  }
}

async function main() {
  console.log('🚀 Starting migration process...')
  
  // Apply migrations in order
  const migrations = [
    '012_update_deposit_requests.sql',
    '013_add_transaction_reference_numbers.sql', 
    '014_update_wallet_functions_with_reference.sql'
  ]
  
  for (const migration of migrations) {
    await executeMigration(migration)
  }
  
  console.log('\n🎉 All migrations completed!')
}

main().catch(console.error)
