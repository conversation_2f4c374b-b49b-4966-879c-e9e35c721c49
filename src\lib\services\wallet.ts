import { supabase, TABLES } from '@/lib/supabase'
import { UserWallet, WalletTransaction, P2PTransfer, DepositRequest } from '@/types'
import { generateTransactionReference } from '@/lib/utils'

/**
 * WalletService - Completely rebuilt to handle Supabase relationships correctly
 *
 * Key fixes:
 * - Uses correct foreign key constraint names from migration files
 * - Robust error handling for all operations
 * - Proper null/empty state handling
 * - Comprehensive logging for debugging
 */
export class WalletService {
  /**
   * Get user's wallet
   */
  static async getUserWallet(userId: string): Promise<UserWallet | null> {
    const { data, error } = await supabase
      .from(TABLES.USER_WALLETS)
      .select('*')
      .eq('user_id', userId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        // No wallet found, create one
        return await this.createUserWallet(userId)
      }
      throw new Error(`Failed to fetch wallet: ${error.message}`)
    }

    return data
  }

  /**
   * Create user wallet
   */
  static async createUserWallet(userId: string): Promise<UserWallet> {
    const { data, error } = await supabase
      .from(TABLES.USER_WALLETS)
      .insert({
        user_id: userId,
        balance: 0.00,
        currency: 'LKR'
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create wallet: ${error.message}`)
    }

    return data
  }

  /**
   * Get wallet transactions with pagination
   */
  static async getWalletTransactions(
    userId: string,
    page: number = 1,
    limit: number = 20,
    filters: {
      transaction_type?: string
      status?: string
      date_from?: string
      date_to?: string
    } = {}
  ): Promise<{
    transactions: WalletTransaction[]
    total: number
  }> {
    const offset = (page - 1) * limit

    // First get the user's wallet
    const wallet = await this.getUserWallet(userId)
    if (!wallet) {
      return { transactions: [], total: 0 }
    }

    let query = supabase
      .from(TABLES.WALLET_TRANSACTIONS)
      .select('*', { count: 'exact' })
      .eq('wallet_id', wallet.id)
      .order('created_at', { ascending: false })

    // Apply filters
    if (filters.transaction_type) {
      query = query.eq('transaction_type', filters.transaction_type)
    }

    if (filters.status) {
      query = query.eq('status', filters.status)
    }

    if (filters.date_from) {
      query = query.gte('created_at', filters.date_from)
    }

    if (filters.date_to) {
      query = query.lte('created_at', filters.date_to)
    }

    const { data, error, count } = await query.range(offset, offset + limit - 1)

    if (error) {
      throw new Error(`Failed to fetch transactions: ${error.message}`)
    }

    return {
      transactions: data || [],
      total: count || 0
    }
  }

  /**
   * Create P2P transfer
   */
  static async createP2PTransfer(
    senderId: string,
    receiverEmail: string,
    amount: number,
    description?: string
  ): Promise<P2PTransfer> {
    // First, find the receiver by email
    const { data: receiver, error: receiverError } = await supabase
      .from(TABLES.USERS)
      .select('id, email, full_name')
      .eq('email', receiverEmail)
      .single()

    if (receiverError || !receiver) {
      throw new Error('Receiver not found')
    }

    if (receiver.id === senderId) {
      throw new Error('Cannot transfer to yourself')
    }

    // Check sender's balance
    const senderWallet = await this.getUserWallet(senderId)
    if (!senderWallet || senderWallet.balance < amount) {
      throw new Error('Insufficient balance')
    }

    // Call the database function to process the transfer
    const { data, error } = await supabase.rpc('process_p2p_transfer', {
      p_sender_id: senderId,
      p_receiver_id: receiver.id,
      p_amount: amount,
      p_description: description
    })

    if (error) {
      throw new Error(`Transfer failed: ${error.message}`)
    }

    // Fetch the created transfer with proper error handling
    console.log(`Fetching created transfer with ID: ${data}`)

    const { data: transfer, error: transferError } = await supabase
      .from(TABLES.P2P_TRANSFERS)
      .select(`
        *,
        sender:auth.users!p2p_transfers_sender_id_fkey(id, email),
        receiver:auth.users!p2p_transfers_receiver_id_fkey(id, email)
      `)
      .eq('id', data)
      .single()

    if (transferError) {
      console.error('Error fetching transfer details:', transferError)
      throw new Error(`Failed to fetch transfer details: ${transferError.message}`)
    }

    if (!transfer) {
      throw new Error('Transfer was created but could not be retrieved')
    }

    console.log('Transfer created successfully:', transfer.id)
    return transfer
  }

  /**
   * Get user's P2P transfers with proper error handling
   */
  static async getUserTransfers(
    userId: string,
    page: number = 1,
    limit: number = 20
  ): Promise<{
    transfers: P2PTransfer[]
    total: number
  }> {
    try {
      if (!userId) {
        throw new Error('User ID is required')
      }

      const offset = (page - 1) * limit

      console.log(`Fetching P2P transfers for user: ${userId}, page: ${page}, limit: ${limit}`)

      const { data, error, count } = await supabase
        .from(TABLES.P2P_TRANSFERS)
        .select(`
          *,
          sender:auth.users!p2p_transfers_sender_id_fkey(id, email),
          receiver:auth.users!p2p_transfers_receiver_id_fkey(id, email)
        `, { count: 'exact' })
        .or(`sender_id.eq.${userId},receiver_id.eq.${userId}`)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1)

      if (error) {
        console.error('Error fetching P2P transfers:', error)
        throw new Error(`Failed to fetch transfers: ${error.message}`)
      }

      console.log(`Successfully fetched ${data?.length || 0} transfers`)

      return {
        transfers: data || [],
        total: count || 0
      }
    } catch (error) {
      console.error('WalletService.getUserTransfers error:', error)
      throw error
    }
  }

  /**
   * Create deposit request with new structure and reference number
   */
  static async createDepositRequest(
    userId: string,
    depositData: {
      amount: number
      depositor_name: string
      deposit_slip_url?: string
      notes?: string
    }
  ): Promise<DepositRequest> {
    const referenceNumber = generateTransactionReference()

    const { data, error } = await supabase
      .from(TABLES.DEPOSIT_REQUESTS)
      .insert({
        user_id: userId,
        amount: depositData.amount,
        currency: 'LKR',
        depositor_name: depositData.depositor_name,
        deposit_slip_url: depositData.deposit_slip_url,
        notes: depositData.notes,
        reference_number: referenceNumber,
        status: 'pending'
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create deposit request: ${error.message}`)
    }

    return data
  }

  /**
   * Get user's deposit requests with comprehensive error handling
   */
  static async getUserDepositRequests(
    userId: string,
    page: number = 1,
    limit: number = 20
  ): Promise<{
    requests: DepositRequest[]
    total: number
  }> {
    try {
      if (!userId) {
        throw new Error('User ID is required')
      }

      const offset = (page - 1) * limit

      console.log(`Fetching deposit requests for user: ${userId}, page: ${page}, limit: ${limit}`)

      const { data, error, count } = await supabase
        .from(TABLES.DEPOSIT_REQUESTS)
        .select('*', { count: 'exact' })
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1)

      if (error) {
        console.error('Error fetching deposit requests:', error)
        throw new Error(`Failed to fetch deposit requests: ${error.message}`)
      }

      console.log(`Successfully fetched ${data?.length || 0} deposit requests`)

      return {
        requests: data || [],
        total: count || 0
      }
    } catch (error) {
      console.error('WalletService.getUserDepositRequests error:', error)
      throw error
    }
  }

  /**
   * Get all deposit requests (admin only) with comprehensive error handling
   */
  static async getAllDepositRequests(
    page: number = 1,
    limit: number = 20,
    status?: string
  ): Promise<{
    requests: DepositRequest[]
    total: number
  }> {
    try {
      const offset = (page - 1) * limit

      console.log(`Fetching all deposit requests - page: ${page}, limit: ${limit}, status: ${status || 'all'}`)

      let query = supabase
        .from(TABLES.DEPOSIT_REQUESTS)
        .select(`
          *,
          user:users!deposit_requests_user_id_fkey(id, full_name, email)
        `, { count: 'exact' })
        .order('created_at', { ascending: false })

      if (status && status !== 'all') {
        query = query.eq('status', status)
      }

      const { data, error, count } = await query.range(offset, offset + limit - 1)

      if (error) {
        console.error('Error fetching all deposit requests:', error)
        throw new Error(`Failed to fetch deposit requests: ${error.message}`)
      }

      console.log(`Successfully fetched ${data?.length || 0} deposit requests (total: ${count || 0})`)

      return {
        requests: data || [],
        total: count || 0
      }
    } catch (error) {
      console.error('WalletService.getAllDepositRequests error:', error)
      throw error
    }
  }

  /**
   * Create a new deposit request with comprehensive validation
   */
  static async createDepositRequest(
    userId: string,
    requestData: {
      amount: number
      bank_name: string
      account_holder_name: string
      account_number: string
      transaction_reference?: string
      notes?: string
    }
  ): Promise<DepositRequest> {
    try {
      if (!userId) {
        throw new Error('User ID is required')
      }

      if (!requestData.amount || requestData.amount <= 0) {
        throw new Error('Valid amount is required')
      }

      if (!requestData.bank_name?.trim()) {
        throw new Error('Bank name is required')
      }

      if (!requestData.account_holder_name?.trim()) {
        throw new Error('Account holder name is required')
      }

      if (!requestData.account_number?.trim()) {
        throw new Error('Account number is required')
      }

      console.log(`Creating deposit request for user: ${userId}, amount: ${requestData.amount}`)

      const { data, error } = await supabase
        .from(TABLES.DEPOSIT_REQUESTS)
        .insert({
          user_id: userId,
          amount: requestData.amount,
          currency: 'LKR',
          bank_name: requestData.bank_name.trim(),
          account_holder_name: requestData.account_holder_name.trim(),
          account_number: requestData.account_number.trim(),
          transaction_reference: requestData.transaction_reference?.trim() || null,
          notes: requestData.notes?.trim() || null,
          status: 'pending'
        })
        .select(`
          *,
          user:users!deposit_requests_user_id_fkey(id, full_name, email)
        `)
        .single()

      if (error) {
        console.error('Error creating deposit request:', error)
        throw new Error(`Failed to create deposit request: ${error.message}`)
      }

      if (!data) {
        throw new Error('Deposit request was not created')
      }

      console.log('Deposit request created successfully:', data.id)
      return data
    } catch (error) {
      console.error('WalletService.createDepositRequest error:', error)
      throw error
    }
  }

  /**
   * Approve deposit request (admin only) with comprehensive error handling
   */
  static async approveDepositRequest(
    requestId: string,
    adminId: string,
    adminNotes?: string
  ): Promise<void> {
    try {
      if (!requestId) {
        throw new Error('Request ID is required')
      }

      if (!adminId) {
        throw new Error('Admin ID is required')
      }

      console.log(`Approving deposit request: ${requestId} by admin: ${adminId}`)

      // Get the deposit request
      const { data: request, error: requestError } = await supabase
        .from(TABLES.DEPOSIT_REQUESTS)
        .select('*')
        .eq('id', requestId)
        .single()

      if (requestError) {
        console.error('Error fetching deposit request:', requestError)
        throw new Error(`Deposit request not found: ${requestError.message}`)
      }

      if (!request) {
        throw new Error('Deposit request not found')
      }

      if (request.status !== 'pending') {
        throw new Error(`Deposit request is ${request.status}, not pending`)
      }

      // Get user's wallet
      const wallet = await this.getUserWallet(request.user_id)
      if (!wallet) {
        throw new Error('User wallet not found')
      }

      // Update wallet balance using the database function
      const { data: transactionId, error: balanceError } = await supabase.rpc('update_wallet_balance', {
        p_wallet_id: wallet.id,
        p_amount: request.amount,
        p_transaction_type: 'deposit',
        p_description: `Bank deposit - ${request.bank_name}`,
        p_reference_id: requestId,
        p_reference_type: 'deposit_request'
      })

      if (balanceError) {
        console.error('Error updating wallet balance:', balanceError)
        throw new Error(`Failed to update wallet balance: ${balanceError.message}`)
      }

      // Update deposit request status
      const { error: updateError } = await supabase
        .from(TABLES.DEPOSIT_REQUESTS)
        .update({
          status: 'approved',
          approved_by: adminId,
          approved_at: new Date().toISOString(),
          admin_notes: adminNotes?.trim() || null,
          wallet_transaction_id: transactionId,
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId)

      if (updateError) {
        console.error('Error updating deposit request:', updateError)
        throw new Error(`Failed to approve deposit request: ${updateError.message}`)
      }

      console.log(`Deposit request ${requestId} approved successfully`)
    } catch (error) {
      console.error('WalletService.approveDepositRequest error:', error)
      throw error
    }
  }

  /**
   * Reject deposit request (admin only) with comprehensive error handling
   */
  static async rejectDepositRequest(
    requestId: string,
    adminId: string,
    adminNotes: string
  ): Promise<void> {
    try {
      if (!requestId) {
        throw new Error('Request ID is required')
      }

      if (!adminId) {
        throw new Error('Admin ID is required')
      }

      if (!adminNotes?.trim()) {
        throw new Error('Admin notes are required for rejection')
      }

      console.log(`Rejecting deposit request: ${requestId} by admin: ${adminId}`)

      // First check if the request exists and is pending
      const { data: request, error: fetchError } = await supabase
        .from(TABLES.DEPOSIT_REQUESTS)
        .select('id, status')
        .eq('id', requestId)
        .single()

      if (fetchError) {
        console.error('Error fetching deposit request:', fetchError)
        throw new Error(`Deposit request not found: ${fetchError.message}`)
      }

      if (!request) {
        throw new Error('Deposit request not found')
      }

      if (request.status !== 'pending') {
        throw new Error(`Deposit request is ${request.status}, not pending`)
      }

      const { error } = await supabase
        .from(TABLES.DEPOSIT_REQUESTS)
        .update({
          status: 'rejected',
          approved_by: adminId,
          approved_at: new Date().toISOString(),
          admin_notes: adminNotes.trim(),
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId)

      if (error) {
        console.error('Error rejecting deposit request:', error)
        throw new Error(`Failed to reject deposit request: ${error.message}`)
      }

      console.log(`Deposit request ${requestId} rejected successfully`)
    } catch (error) {
      console.error('WalletService.rejectDepositRequest error:', error)
      throw error
    }
  }
}
