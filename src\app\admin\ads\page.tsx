'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import {
  FileText,
  Search,
  Filter,
  MoreVertical,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  Trash2,
  Calendar,
  DollarSign,
  MapPin,
  Star,
  X,
  ChevronDown
} from 'lucide-react'
import AdminLayout from '@/components/admin/AdminLayout'
import { AdminService } from '@/lib/services/admin'
import { CategoryService } from '@/lib/services/categories'
import { AdWithDetails, Category } from '@/types'
import { SRI_LANKAN_DISTRICTS } from '@/lib/constants'
import { showConfirmation } from '@/components/ui/ConfirmationDialog'

interface AdActionsProps {
  ad: AdWithDetails
  onStatusUpdate: (adId: string, status: 'active' | 'expired' | 'sold' | 'draft') => void
  onDelete: (adId: string) => void
  onToggleFeatured: (adId: string, featured: boolean) => void
}

function AdActions({ ad, onStatusUpdate, onDelete, onToggleFeatured }: AdActionsProps) {
  const [showMenu, setShowMenu] = useState(false)

  return (
    <div className="relative">
      <button
        onClick={() => setShowMenu(!showMenu)}
        className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
      >
        <MoreVertical className="h-4 w-4" />
      </button>
      
      {showMenu && (
        <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
          <div className="py-1">
            <a
              href={`/ad/${ad.id}`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center w-full px-4 py-2 text-sm text-primary-blue hover:bg-primary-blue/10"
              onClick={() => setShowMenu(false)}
            >
              <Eye className="h-4 w-4 mr-2" />
              Preview Ad
            </a>
            {ad.status === 'pending' && (
              <>
                <button
                  onClick={() => {
                    onStatusUpdate(ad.id, 'active')
                    setShowMenu(false)
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-green-600 hover:bg-green-50"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Approve Ad
                </button>
                <button
                  onClick={() => {
                    onStatusUpdate(ad.id, 'expired')
                    setShowMenu(false)
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  Mark as Expired
                </button>
              </>
            )}
            {ad.status === 'active' && (
              <button
                onClick={() => {
                  onStatusUpdate(ad.id, 'rejected')
                  setShowMenu(false)
                }}
                className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
              >
                <XCircle className="h-4 w-4 mr-2" />
                Deactivate Ad
              </button>
            )}
            {ad.status === 'active' && (
              <button
                onClick={() => {
                  onToggleFeatured(ad.id, !ad.featured)
                  setShowMenu(false)
                }}
                className={`flex items-center w-full px-4 py-2 text-sm ${
                  ad.featured
                    ? 'text-orange-600 hover:bg-orange-50'
                    : 'text-yellow-600 hover:bg-yellow-50'
                }`}
              >
                <Star className={`h-4 w-4 mr-2 ${ad.featured ? 'fill-current' : ''}`} />
                {ad.featured ? 'Remove Featured' : 'Make Featured'}
              </button>
            )}
            <button
              onClick={async () => {
                const confirmed = await showConfirmation({
                  title: 'Delete Ad',
                  message: 'Are you sure you want to delete this ad? This action cannot be undone.',
                  confirmText: 'Delete',
                  cancelText: 'Cancel',
                  variant: 'danger'
                })

                if (confirmed) {
                  onDelete(ad.id)
                }
                setShowMenu(false)
              }}
              className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Ad
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default function AdminAds() {
  const searchParams = useSearchParams()
  const initialStatus = searchParams.get('status') as 'active' | 'pending' | 'pending_new' | 'pending_edited' | 'sold' | 'expired' | 'draft' | null

  const [ads, setAds] = useState<AdWithDetails[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalAds, setTotalAds] = useState(0)
  const [showFilters, setShowFilters] = useState(false)
  const [pendingNewCount, setPendingNewCount] = useState(0)
  const [pendingEditedCount, setPendingEditedCount] = useState(0)

  // Filter states
  const [filters, setFilters] = useState({
    status: initialStatus || 'all',
    category_id: '',
    location: '',
    date_from: '',
    date_to: '',
    featured: 'all', // Changed from '' to 'all' to fix the default filter
    search: ''
  })

  const adsPerPage = 20

  useEffect(() => {
    fetchCategories()
    fetchPendingCounts()
  }, [])

  useEffect(() => {
    fetchAds()
  }, [currentPage, filters])

  const fetchCategories = async () => {
    try {
      const categoriesData = await CategoryService.getAllCategories()
      setCategories(categoriesData)
    } catch (err) {
      console.error('Error fetching categories:', err)
    }
  }

  const fetchPendingCounts = async () => {
    try {
      const [newPendingResult, editedPendingResult] = await Promise.all([
        AdminService.getAllAds({ status: 'pending', is_edited: false }, 1, 1),
        AdminService.getAllAds({ status: 'pending', is_edited: true }, 1, 1)
      ])
      setPendingNewCount(newPendingResult.total)
      setPendingEditedCount(editedPendingResult.total)
    } catch (err) {
      console.error('Error fetching pending counts:', err)
    }
  }

  const fetchAds = async () => {
    try {
      setLoading(true)

      // Handle special filter cases for pending ads
      let statusFilter = filters.status
      let isEditedFilter = undefined

      if (filters.status === 'pending_new') {
        statusFilter = 'pending'
        isEditedFilter = false
      } else if (filters.status === 'pending_edited') {
        statusFilter = 'pending'
        isEditedFilter = true
      }

      const filterParams = {
        status: statusFilter === 'all' ? undefined : statusFilter as any,
        is_edited: isEditedFilter,
        category_id: filters.category_id || undefined,
        location: filters.location || undefined,
        date_from: filters.date_from || undefined,
        date_to: filters.date_to || undefined,
        featured: filters.featured === 'all' ? undefined : filters.featured === 'true',
        search: searchTerm || filters.search || undefined
      }

      const { ads: adsData, total } = await AdminService.getAllAds(filterParams, currentPage, adsPerPage)
      setAds(adsData)
      setTotalAds(total)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load ads')
    } finally {
      setLoading(false)
    }
  }

  const handleStatusUpdate = async (adId: string, status: 'active' | 'expired' | 'sold' | 'draft') => {
    try {
      await AdminService.updateAdStatus(adId, status)
      await fetchAds() // Refresh the list
      await fetchPendingCounts() // Refresh pending counts
    } catch (err) {
      alert('Failed to update ad status')
    }
  }

  const handleDelete = async (adId: string) => {
    try {
      await AdminService.deleteAd(adId)
      await fetchAds() // Refresh the list
      await fetchPendingCounts() // Refresh pending counts
    } catch (err) {
      alert('Failed to delete ad')
    }
  }

  const handleToggleFeatured = async (adId: string, featured: boolean) => {
    try {
      await AdminService.toggleAdFeatured(adId, featured)
      await fetchAds() // Refresh the list
    } catch (err) {
      alert('Failed to update featured status')
    }
  }

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    setCurrentPage(1) // Reset to first page when filters change
  }

  const handleClearFilters = () => {
    setFilters({
      status: 'all',
      category_id: '',
      location: '',
      date_from: '',
      date_to: '',
      featured: '',
      search: ''
    })
    setSearchTerm('')
    setCurrentPage(1)
  }

  const hasActiveFilters = Object.values(filters).some(value => value && value !== 'all') || searchTerm

  // Don't apply client-side filtering since server-side filtering is already applied
  const filteredAds = ads

  const totalPages = Math.ceil(totalAds / adsPerPage)

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-orange-100 text-orange-800'
      case 'rejected': return 'bg-red-100 text-red-800'
      case 'expired': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return CheckCircle
      case 'pending': return Clock
      case 'rejected': return XCircle
      case 'expired': return Calendar
      default: return FileText
    }
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-300 rounded w-1/4 mb-6"></div>
          <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="h-10 bg-gray-300 rounded w-1/3"></div>
            </div>
            <div className="divide-y divide-gray-200">
              {Array.from({ length: 10 }).map((_, i) => (
                <div key={i} className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="h-16 w-16 bg-gray-300 rounded-lg"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-300 rounded w-1/3 mb-2"></div>
                      <div className="h-3 bg-gray-300 rounded w-1/2 mb-2"></div>
                      <div className="h-3 bg-gray-300 rounded w-1/4"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Ads Management</h1>
            <p className="text-gray-600">Review and manage classified ads</p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <FileText className="h-4 w-4" />
              <span>{totalAds} total ads</span>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-orange-600">
                <Clock className="h-4 w-4" />
                <span>{pendingNewCount} new pending</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-blue-600">
                <Clock className="h-4 w-4" />
                <span>{pendingEditedCount} edited pending</span>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          {/* Main Filter Row */}
          <div className="flex flex-col lg:flex-row gap-4 mb-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search ads by title, description, or user..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                />
              </div>
            </div>
            <div className="flex gap-3">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`flex items-center gap-2 px-4 py-2 border rounded-lg transition-colors ${
                  showFilters || hasActiveFilters
                    ? 'bg-blue-50 border-blue-200 text-blue-700'
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                <Filter className="h-4 w-4" />
                <span>Filters</span>
                {hasActiveFilters && (
                  <span className="bg-blue-600 text-white text-xs rounded-full px-2 py-0.5">
                    {Object.values(filters).filter(v => v && v !== 'all').length + (searchTerm ? 1 : 0)}
                  </span>
                )}
              </button>
              {hasActiveFilters && (
                <button
                  onClick={handleClearFilters}
                  className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  <X className="h-4 w-4" />
                  <span>Clear</span>
                </button>
              )}
            </div>
          </div>

          {/* Advanced Filters */}
          {showFilters && (
            <div className="border-t border-gray-200 pt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Status Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                  <select
                    value={filters.status}
                    onChange={(e) => handleFilterChange('status', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  >
                    <option value="all">All Status</option>
                    <option value="active">Active</option>
                    <option value="pending">All Pending</option>
                    <option value="pending_new">New Ad Approvals</option>
                    <option value="pending_edited">Edited Ad Approvals</option>
                    <option value="sold">Sold</option>
                    <option value="expired">Expired</option>
                    <option value="draft">Draft</option>
                  </select>
                </div>

                {/* Category Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                  <select
                    value={filters.category_id}
                    onChange={(e) => handleFilterChange('category_id', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  >
                    <option value="">All Categories</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Location Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                  <select
                    value={filters.location}
                    onChange={(e) => handleFilterChange('location', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  >
                    <option value="">All Locations</option>
                    {SRI_LANKAN_DISTRICTS.map((district) => (
                      <option key={district.slug} value={district.name}>
                        {district.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Featured Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Featured</label>
                  <select
                    value={filters.featured}
                    onChange={(e) => handleFilterChange('featured', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  >
                    <option value="all">All Ads</option>
                    <option value="true">Featured Only</option>
                    <option value="false">Non-Featured</option>
                  </select>
                </div>
              </div>

              {/* Date Range Filters */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Date From</label>
                  <input
                    type="date"
                    value={filters.date_from}
                    onChange={(e) => handleFilterChange('date_from', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Date To</label>
                  <input
                    type="date"
                    value={filters.date_to}
                    onChange={(e) => handleFilterChange('date_to', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Ads List */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="divide-y divide-gray-200">
            {filteredAds.map((ad) => {
              const StatusIcon = getStatusIcon(ad.status)
              return (
                <div key={ad.id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-start space-x-4">
                    {/* Ad Image */}
                    <div className="h-20 w-20 bg-gray-200 rounded-lg flex-shrink-0 overflow-hidden border border-gray-300">
                      {ad.ad_images && ad.ad_images.length > 0 ? (
                        <div className="relative h-full w-full">
                          <img
                            src={ad.ad_images[0].image_url}
                            alt={ad.title}
                            className="h-full w-full object-cover"
                          />
                          {ad.ad_images.length > 1 && (
                            <div className="absolute bottom-1 right-1 bg-black/60 text-white text-xs px-1 rounded">
                              +{ad.ad_images.length - 1}
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="h-full w-full flex items-center justify-center">
                          <FileText className="h-8 w-8 text-gray-400" />
                        </div>
                      )}
                    </div>

                    {/* Ad Details */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <h3 className="text-lg font-medium text-gray-900 truncate">
                              {ad.title}
                            </h3>
                            {ad.is_edited && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                Edited
                              </span>
                            )}
                            {ad.featured && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <Star className="h-3 w-3 mr-1 fill-current" />
                                Featured
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                            {ad.description}
                          </p>
                          {ad.is_edited && ad.edit_reason && (
                            <p className="text-xs text-orange-600 mt-1 italic">
                              Edit reason: {ad.edit_reason}
                            </p>
                          )}
                          
                          <div className="flex items-center space-x-4 mt-3 text-sm text-gray-500">
                            <div className="flex items-center">
                              <DollarSign className="h-3 w-3 mr-1" />
                              Rs {ad.price?.toLocaleString() || 'N/A'}
                            </div>
                            <div className="flex items-center">
                              <MapPin className="h-3 w-3 mr-1" />
                              {ad.location || 'No location'}
                            </div>
                            <div className="flex items-center">
                              <Eye className="h-3 w-3 mr-1" />
                              {ad.views || 0} views
                            </div>
                            <div className="flex items-center">
                              <Calendar className="h-3 w-3 mr-1" />
                              {ad.is_edited ? 'Updated: ' : 'Created: '}
                              {new Date(ad.is_edited ? ad.updated_at : ad.created_at).toLocaleDateString()}
                            </div>
                          </div>

                          <div className="flex items-center space-x-2 mt-2">
                            <span className="text-sm text-gray-600">
                              By: {ad.user?.full_name || 'Unknown User'}
                            </span>
                            <span className="text-gray-300">•</span>
                            <span className="text-sm text-gray-600">
                              {ad.category?.name} → {ad.subcategory?.name}
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center space-x-3 ml-4">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(ad.status)}`}>
                            <StatusIcon className="h-3 w-3 mr-1" />
                            {ad.status === 'pending' && ad.is_edited
                              ? 'Edited Approval'
                              : ad.status.charAt(0).toUpperCase() + ad.status.slice(1)
                            }
                          </span>
                          <AdActions
                            ad={ad}
                            onStatusUpdate={handleStatusUpdate}
                            onDelete={handleDelete}
                            onToggleFeatured={handleToggleFeatured}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing {((currentPage - 1) * adsPerPage) + 1} to {Math.min(currentPage * adsPerPage, totalAds)} of {totalAds} ads
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
