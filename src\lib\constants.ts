// Color palette as defined in requirements
export const COLORS = {
  PRIMARY_BLUE: '#00559F',
  RED_ACCENT: '#EC2100',
  ORANGE_ACCENT: '#FF7900',
  SECONDARY_BLUE: '#007CB3',
  GRAY_NEUTRAL: '#A39D98',
} as const

// Application constants
export const APP_CONFIG = {
  NAME: 'OKDOI',
  DESCRIPTION: 'Premium Marketplace for Everything',
  MAX_IMAGES_PER_AD: 10,
  MAX_IMAGE_SIZE: 5 * 1024 * 1024, // 5MB
  SUPPORTED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  AD_EXPIRY_DAYS: 30,
  PAGINATION_LIMIT: 20,
} as const

// Currency options - Only Sri Lankan Rupee for OKDOI marketplace
export const CURRENCIES = [
  { code: 'LKR', symbol: 'Rs', name: 'Sri Lankan Rupee' },
] as const

// Default currency for Sri Lankan website
export const DEFAULT_CURRENCY = 'LKR'

// Import location data
import { SRI_LANKAN_LOCATIONS } from '@/lib/data/locations'

// Sri Lankan Districts (25 districts organized by provinces) - derived from location data
export const SRI_LANKAN_DISTRICTS = SRI_LANKAN_LOCATIONS.map(district => ({
  name: district.name,
  province: district.province,
  slug: district.id
})) as const

// All cities from all districts
export const SRI_LANKAN_CITIES = SRI_LANKAN_LOCATIONS.flatMap(district =>
  district.cities.map(city => ({
    name: city.name,
    district: district.name,
    province: district.province,
    slug: city.id,
    district_slug: district.id
  }))
) as const

// Sri Lankan Provinces
export const SRI_LANKAN_PROVINCES = [
  'Western',
  'Central',
  'Southern',
  'Northern',
  'Eastern',
  'North Western',
  'North Central',
  'Uva',
  'Sabaragamuwa'
] as const

// Condition options
export const CONDITIONS = [
  { value: 'new', label: 'New' },
  { value: 'used', label: 'Used' },
  { value: 'refurbished', label: 'Refurbished' },
] as const

// Sort options
export const SORT_OPTIONS = [
  { value: 'newest', label: 'Newest First' },
  { value: 'oldest', label: 'Oldest First' },
  { value: 'price_low', label: 'Price: Low to High' },
  { value: 'price_high', label: 'Price: High to Low' },
  { value: 'most_viewed', label: 'Most Viewed' },
] as const
