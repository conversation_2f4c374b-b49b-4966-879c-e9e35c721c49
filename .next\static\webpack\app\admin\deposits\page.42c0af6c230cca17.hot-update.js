"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/deposits/page",{

/***/ "(app-pages-browser)/./src/app/admin/deposits/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/admin/deposits/page.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminDepositsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/admin/AdminLayout */ \"(app-pages-browser)/./src/components/admin/AdminLayout.tsx\");\n/* harmony import */ var _lib_services_wallet__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/wallet */ \"(app-pages-browser)/./src/lib/services/wallet.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nfunction DepositActions(param) {\n    let { deposit, onApprove, onReject } = param;\n    _s();\n    const [showMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setShowMenu(!showMenu),\n                className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-1\",\n                    children: deposit.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    onApprove(deposit.id);\n                                    setShowMenu(false);\n                                },\n                                className: \"flex items-center w-full px-4 py-2 text-sm text-green-600 hover:bg-green-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Approve Deposit\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    onReject(deposit.id);\n                                    setShowMenu(false);\n                                },\n                                className: \"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Reject Deposit\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n_s(DepositActions, \"2FjIcsdimgVhm2IsUWodA2ftTZU=\");\n_c = DepositActions;\nfunction AdminDepositsPage() {\n    var _selectedDeposit_user;\n    _s1();\n    const [deposits, setDeposits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalDeposits, setTotalDeposits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const depositsPerPage = 20;\n    // Modal states\n    const [showRejectModal, setShowRejectModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showModifyModal, setShowModifyModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedDeposit, setSelectedDeposit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rejectNotes, setRejectNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [modifiedAmount, setModifiedAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchDeposits();\n    }, [\n        currentPage,\n        selectedStatus\n    ]);\n    const fetchDeposits = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const status = selectedStatus === \"all\" ? undefined : selectedStatus;\n            const { requests, total } = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_3__.WalletService.getAllDepositRequests(currentPage, depositsPerPage, status);\n            setDeposits(requests || []);\n            setTotalDeposits(total || 0);\n        } catch (err) {\n            console.error(\"Error fetching deposits:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to load deposits\");\n            setDeposits([]);\n            setTotalDeposits(0);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleApprove = async (depositId)=>{\n        if (!confirm(\"Are you sure you want to approve this deposit?\")) return;\n        try {\n            setActionLoading(true);\n            const { data: { user } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_5__.supabase.auth.getUser();\n            if (!user) throw new Error(\"Not authenticated\");\n            await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_3__.WalletService.approveDepositRequest(depositId, user.id);\n            await fetchDeposits();\n            alert(\"Deposit approved successfully!\");\n        } catch (err) {\n            alert(err instanceof Error ? err.message : \"Failed to approve deposit\");\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleReject = (depositId)=>{\n        const deposit = deposits.find((d)=>d.id === depositId);\n        if (!deposit) return;\n        setSelectedDeposit(deposit);\n        setShowRejectModal(true);\n    };\n    const handleModifyAmount = (depositId)=>{\n        const deposit = deposits.find((d)=>d.id === depositId);\n        if (!deposit) return;\n        setSelectedDeposit(deposit);\n        setModifiedAmount(deposit.amount.toString());\n        setShowModifyModal(true);\n    };\n    const confirmReject = async ()=>{\n        if (!selectedDeposit || !rejectNotes.trim()) return;\n        try {\n            setActionLoading(true);\n            const { data: { user } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_5__.supabase.auth.getUser();\n            if (!user) throw new Error(\"Not authenticated\");\n            await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_3__.WalletService.rejectDepositRequest(selectedDeposit.id, user.id, rejectNotes);\n            setShowRejectModal(false);\n            setSelectedDeposit(null);\n            setRejectNotes(\"\");\n            await fetchDeposits();\n            alert(\"Deposit rejected successfully!\");\n        } catch (err) {\n            alert(err instanceof Error ? err.message : \"Failed to reject deposit\");\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"approved\":\n                return \"text-green-600 bg-green-100\";\n            case \"pending\":\n                return \"text-yellow-600 bg-yellow-100\";\n            case \"rejected\":\n                return \"text-red-600 bg-red-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"approved\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 16\n                }, this);\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 16\n                }, this);\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const filteredDeposits = deposits.filter((deposit)=>{\n        var _deposit_user_full_name, _deposit_user, _deposit_user_email, _deposit_user1;\n        const matchesSearch = ((_deposit_user = deposit.user) === null || _deposit_user === void 0 ? void 0 : (_deposit_user_full_name = _deposit_user.full_name) === null || _deposit_user_full_name === void 0 ? void 0 : _deposit_user_full_name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_deposit_user1 = deposit.user) === null || _deposit_user1 === void 0 ? void 0 : (_deposit_user_email = _deposit_user1.email) === null || _deposit_user_email === void 0 ? void 0 : _deposit_user_email.toLowerCase().includes(searchTerm.toLowerCase())) || deposit.bank_name.toLowerCase().includes(searchTerm.toLowerCase()) || deposit.account_holder_name.toLowerCase().includes(searchTerm.toLowerCase());\n        return matchesSearch;\n    });\n    const totalPages = Math.ceil(totalDeposits / depositsPerPage);\n    const statusCounts = {\n        all: totalDeposits,\n        pending: deposits.filter((d)=>d.status === \"pending\").length,\n        approved: deposits.filter((d)=>d.status === \"approved\").length,\n        rejected: deposits.filter((d)=>d.status === \"rejected\").length\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Deposit Requests\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Review and manage user deposit requests\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            totalDeposits,\n                                            \" total requests\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                        children: Object.entries(statusCounts).map((param)=>{\n                            let [status, count] = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>setSelectedStatus(status),\n                                className: \"p-4 rounded-lg border cursor-pointer transition-colors \".concat(selectedStatus === status ? \"border-primary-blue bg-primary-blue/5\" : \"border-gray-200 hover:border-gray-300\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: count\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600 capitalize\",\n                                        children: status === \"all\" ? \"Total Requests\" : \"\".concat(status, \" Requests\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, status, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search by user, bank, or account holder...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 13\n                        }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: fetchDeposits,\n                                    className: \"px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90\",\n                                    children: \"Try Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full divide-y divide-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"User\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Amount\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Depositor & Reference\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Submitted\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-white divide-y divide-gray-200\",\n                                        children: filteredDeposits.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                colSpan: 6,\n                                                className: \"px-6 py-12 text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-12 w-12 text-gray-400 mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                            children: \"No deposit requests found\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500\",\n                                                            children: searchTerm ? \"Try adjusting your search terms.\" : \"No deposit requests have been submitted yet.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 21\n                                        }, this) : filteredDeposits.map((deposit)=>{\n                                            var _deposit_user, _deposit_user1;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-10 w-10 rounded-full bg-primary-blue/10 flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-primary-blue\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                        lineNumber: 329,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                            children: ((_deposit_user = deposit.user) === null || _deposit_user === void 0 ? void 0 : _deposit_user.full_name) || \"No name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                            lineNumber: 332,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: ((_deposit_user1 = deposit.user) === null || _deposit_user1 === void 0 ? void 0 : _deposit_user1.email) || \"No email\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                            lineNumber: 335,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-bold text-gray-900\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(deposit.amount)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-900\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        \"Depositor: \",\n                                                                        deposit.depositor_name || \"Not specified\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                deposit.reference_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-400 font-mono\",\n                                                                    children: [\n                                                                        \"Ref: \",\n                                                                        deposit.reference_number\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                    lineNumber: 350,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                deposit.deposit_slip_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                        href: deposit.deposit_slip_url,\n                                                                        target: \"_blank\",\n                                                                        rel: \"noopener noreferrer\",\n                                                                        className: \"inline-flex items-center text-xs text-blue-600 hover:text-blue-800\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"h-3 w-3 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                                lineNumber: 362,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            \"View Proof\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(getStatusColor(deposit.status)),\n                                                            children: [\n                                                                getStatusIcon(deposit.status),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-1 capitalize\",\n                                                                    children: deposit.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 text-sm text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    new Date(deposit.created_at).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: new Date(deposit.created_at).toLocaleTimeString()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                lineNumber: 380,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DepositActions, {\n                                                            deposit: deposit,\n                                                            onApprove: handleApprove,\n                                                            onReject: handleReject\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, deposit.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 9\n                    }, this),\n                    totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-700\",\n                                children: [\n                                    \"Showing \",\n                                    (currentPage - 1) * depositsPerPage + 1,\n                                    \" to \",\n                                    Math.min(currentPage * depositsPerPage, totalDeposits),\n                                    \" of \",\n                                    totalDeposits,\n                                    \" requests\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setCurrentPage((prev)=>Math.max(prev - 1, 1)),\n                                        disabled: currentPage === 1,\n                                        className: \"px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\",\n                                        children: \"Previous\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 15\n                                    }, this),\n                                    Array.from({\n                                        length: Math.min(5, totalPages)\n                                    }, (_, i)=>{\n                                        const page = i + Math.max(1, currentPage - 2);\n                                        return page <= totalPages ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentPage(page),\n                                            className: \"px-3 py-2 border rounded-lg \".concat(currentPage === page ? \"bg-primary-blue text-white border-primary-blue\" : \"border-gray-300 hover:bg-gray-50\"),\n                                            children: page\n                                        }, page, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 19\n                                        }, this) : null;\n                                    }),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setCurrentPage((prev)=>Math.min(prev + 1, totalPages)),\n                                        disabled: currentPage === totalPages,\n                                        className: \"px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\",\n                                        children: \"Next\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this),\n            showRejectModal && selectedDeposit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Reject Deposit Request\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mb-2\",\n                                children: [\n                                    \"You are about to reject a deposit request for\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(selectedDeposit.amount)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" \",\n                                    \"from \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: (_selectedDeposit_user = selectedDeposit.user) === null || _selectedDeposit_user === void 0 ? void 0 : _selectedDeposit_user.full_name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 22\n                                    }, this),\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 449,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Reason for rejection *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    required: true,\n                                    value: rejectNotes,\n                                    onChange: (e)=>setRejectNotes(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                    rows: 4,\n                                    placeholder: \"Please provide a reason for rejecting this deposit request...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>{\n                                        setShowRejectModal(false);\n                                        setSelectedDeposit(null);\n                                        setRejectNotes(\"\");\n                                    },\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: confirmReject,\n                                    disabled: actionLoading || !rejectNotes.trim(),\n                                    className: \"flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50\",\n                                    children: actionLoading ? \"Rejecting...\" : \"Reject Deposit\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 447,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                lineNumber: 446,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n        lineNumber: 219,\n        columnNumber: 5\n    }, this);\n}\n_s1(AdminDepositsPage, \"IT94xPlRLdb2MkMAHMGQYy02XII=\");\n_c1 = AdminDepositsPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"DepositActions\");\n$RefreshReg$(_c1, \"AdminDepositsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/deposits/page.tsx\n"));

/***/ })

});