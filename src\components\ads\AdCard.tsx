import Link from 'next/link'
import Image from 'next/image'
import { MapP<PERSON>, Clock, Eye, Heart } from 'lucide-react'
import Card, { CardContent } from '@/components/ui/Card'
import { AdWithDetails } from '@/types'
import { formatCurrency, formatTimeAgo } from '@/lib/utils'

interface AdCardProps {
  ad: AdWithDetails
  showFeaturedBadge?: boolean
}

export default function AdCard({ ad, showFeaturedBadge = true }: AdCardProps) {

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) {
      return 'Just now'
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays}d ago`
    }
  }

  const getConditionColor = (condition: string) => {
    switch (condition) {
      case 'new':
        return 'bg-green-100 text-green-800'
      case 'used':
        return 'bg-yellow-100 text-yellow-800'
      case 'refurbished':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Link href={`/ad/${ad.id}`}>
      <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer group overflow-hidden h-full">
        <div className="relative">
          <div className="w-full h-48 bg-gray-200 relative overflow-hidden">
            {ad.ad_images && ad.ad_images.length > 0 ? (
              <Image
                src={ad.ad_images[0].image_url}
                alt={ad.title}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-200"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-primary-blue/20 to-secondary-blue/20 flex items-center justify-center">
                <span className="text-gray-500 text-sm">No Image</span>
              </div>
            )}
          </div>
          
          {/* Badges */}
          <div className="absolute top-2 left-2 flex flex-col gap-1">
            {showFeaturedBadge && ad.featured && (
              <span className="bg-accent-orange text-white px-2 py-1 rounded text-xs font-semibold">
                Featured
              </span>
            )}
            <span className={`px-2 py-1 rounded text-xs font-medium ${getConditionColor(ad.condition)}`}>
              {ad.condition.charAt(0).toUpperCase() + ad.condition.slice(1)}
            </span>
          </div>

          {/* Favorite Button */}
          <button
            className="absolute top-2 right-2 p-2 bg-white/80 hover:bg-white rounded-full shadow-sm transition-colors"
            onClick={(e) => {
              e.preventDefault()
              // TODO: Implement favorite functionality
            }}
          >
            <Heart className="h-4 w-4 text-gray-600 hover:text-accent-red" />
          </button>
        </div>

        <CardContent className="p-4 flex-1 flex flex-col">
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-primary-blue transition-colors">
              {ad.title}
            </h3>
            
            <p className="text-gray-600 text-sm mb-3 line-clamp-2">
              {ad.description}
            </p>

            <div className="flex items-center text-gray-500 text-sm mb-2">
              <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
              <span className="truncate">{ad.location}</span>
            </div>

            <div className="text-xs text-gray-400 mb-3">
              {ad.category.name}{ad.subcategory ? ` → ${ad.subcategory.name}` : ''}
            </div>
          </div>

          <div className="flex items-center justify-between pt-2 border-t border-gray-100">
            <span className="text-lg font-bold text-primary-blue">
              {formatCurrency(ad.price)}
            </span>
            
            <div className="flex items-center text-gray-400 text-xs space-x-3">
              <div className="flex items-center">
                <Eye className="h-3 w-3 mr-1" />
                {ad.views}
              </div>
              <div className="flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                {formatTimeAgo(ad.created_at)}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
