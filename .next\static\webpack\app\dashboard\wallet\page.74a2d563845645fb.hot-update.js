"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/wallet/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/wallet/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/wallet/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ WalletPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/DashboardLayout */ \"(app-pages-browser)/./src/components/dashboard/DashboardLayout.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_services_wallet__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/services/wallet */ \"(app-pages-browser)/./src/lib/services/wallet.ts\");\n/* harmony import */ var _lib_services_storage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/services/storage */ \"(app-pages-browser)/./src/lib/services/storage.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(app-pages-browser)/./src/components/ui/LoadingSpinner.tsx\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction WalletPage() {\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [wallet, setWallet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [transactions, setTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [transfers, setTransfers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [depositRequests, setDepositRequests] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalBalance: 0,\n        totalDeposits: 0,\n        totalTransfers: 0,\n        pendingDeposits: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    // Modal states\n    const [showTransferModal, setShowTransferModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDepositModal, setShowDepositModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Transfer form\n    const [transferForm, setTransferForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        receiverEmail: \"\",\n        amount: \"\",\n        description: \"\"\n    });\n    const [transferLoading, setTransferLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Deposit form\n    const [depositForm, setDepositForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        amount: \"\",\n        depositor_name: \"\",\n        notes: \"\",\n        terms_accepted: false\n    });\n    const [depositProof, setDepositProof] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [depositLoading, setDepositLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            fetchWalletData();\n        }\n    }, [\n        user\n    ]);\n    const fetchWalletData = async ()=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            setError(null);\n            console.log(\"Fetching wallet data for user:\", user.id);\n            // Fetch wallet with error handling\n            const walletData = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_4__.WalletService.getUserWallet(user.id);\n            setWallet(walletData);\n            console.log(\"Wallet data fetched:\", walletData);\n            // Fetch recent transactions with error handling\n            const { transactions: transactionsData } = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_4__.WalletService.getWalletTransactions(user.id, 1, 10);\n            setTransactions(transactionsData || []);\n            console.log(\"Transactions fetched:\", (transactionsData === null || transactionsData === void 0 ? void 0 : transactionsData.length) || 0);\n            // Fetch recent transfers with error handling\n            const { transfers: transfersData } = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_4__.WalletService.getUserTransfers(user.id, 1, 10);\n            setTransfers(transfersData || []);\n            console.log(\"Transfers fetched:\", (transfersData === null || transfersData === void 0 ? void 0 : transfersData.length) || 0);\n            // Fetch deposit requests with error handling\n            const { requests: depositsData } = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_4__.WalletService.getUserDepositRequests(user.id, 1, 10);\n            setDepositRequests(depositsData || []);\n            console.log(\"Deposit requests fetched:\", (depositsData === null || depositsData === void 0 ? void 0 : depositsData.length) || 0);\n            // Calculate stats with null safety\n            const totalDeposits = (transactionsData || []).filter((t)=>t.transaction_type === \"deposit\" && t.status === \"completed\").reduce((sum, t)=>sum + (t.amount || 0), 0);\n            const totalTransfers = (transfersData || []).filter((t)=>t.sender_id === user.id && t.status === \"completed\").reduce((sum, t)=>sum + (t.amount || 0), 0);\n            const pendingDeposits = (depositsData || []).filter((d)=>d.status === \"pending\").reduce((sum, d)=>sum + (d.amount || 0), 0);\n            setStats({\n                totalBalance: (walletData === null || walletData === void 0 ? void 0 : walletData.balance) || 0,\n                totalDeposits,\n                totalTransfers,\n                pendingDeposits\n            });\n            console.log(\"Wallet data fetch completed successfully\");\n        } catch (err) {\n            console.error(\"Error fetching wallet data:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to load wallet data\");\n            // Reset data on error\n            setWallet(null);\n            setTransactions([]);\n            setTransfers([]);\n            setDepositRequests([]);\n            setStats({\n                totalBalance: 0,\n                totalDeposits: 0,\n                totalTransfers: 0,\n                pendingDeposits: 0\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleTransfer = async (e)=>{\n        e.preventDefault();\n        if (!user) return;\n        try {\n            setTransferLoading(true);\n            await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_4__.WalletService.createP2PTransfer(user.id, transferForm.receiverEmail, parseFloat(transferForm.amount), transferForm.description || undefined);\n            setShowTransferModal(false);\n            setTransferForm({\n                receiverEmail: \"\",\n                amount: \"\",\n                description: \"\"\n            });\n            await fetchWalletData();\n            alert(\"Transfer completed successfully!\");\n        } catch (err) {\n            alert(err instanceof Error ? err.message : \"Transfer failed\");\n        } finally{\n            setTransferLoading(false);\n        }\n    };\n    const handleDeposit = async (e)=>{\n        e.preventDefault();\n        if (!user) return;\n        // Validation\n        if (!depositForm.terms_accepted) {\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__.showAlert)({\n                title: \"Terms Required\",\n                message: \"Please accept the terms and conditions to proceed.\",\n                variant: \"warning\"\n            });\n            return;\n        }\n        if (!depositProof) {\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__.showAlert)({\n                title: \"Proof Required\",\n                message: \"Please upload a deposit receipt or proof of payment.\",\n                variant: \"warning\"\n            });\n            return;\n        }\n        try {\n            setDepositLoading(true);\n            // Upload proof file first\n            let proofUrl = \"\";\n            if (depositProof) {\n                proofUrl = await _lib_services_storage__WEBPACK_IMPORTED_MODULE_5__.StorageService.uploadImage(depositProof, user.id);\n            }\n            await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_4__.WalletService.createDepositRequest(user.id, {\n                amount: parseFloat(depositForm.amount),\n                depositor_name: depositForm.depositor_name,\n                notes: depositForm.notes || undefined,\n                deposit_slip_url: proofUrl\n            });\n            setShowDepositModal(false);\n            setDepositForm({\n                amount: \"\",\n                depositor_name: \"\",\n                notes: \"\",\n                terms_accepted: false\n            });\n            setDepositProof(null);\n            await fetchWalletData();\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__.showAlert)({\n                title: \"Success\",\n                message: \"Deposit request submitted successfully! We will review it shortly.\",\n                variant: \"success\"\n            });\n        } catch (err) {\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__.showAlert)({\n                title: \"Error\",\n                message: err instanceof Error ? err.message : \"Failed to submit deposit request\",\n                variant: \"danger\"\n            });\n        } finally{\n            setDepositLoading(false);\n        }\n    };\n    const getTransactionIcon = (type)=>{\n        switch(type){\n            case \"deposit\":\n            case \"transfer_in\":\n            case \"refund\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 16\n                }, this);\n            case \"withdrawal\":\n            case \"transfer_out\":\n            case \"purchase\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getTransactionColor = (type)=>{\n        switch(type){\n            case \"deposit\":\n            case \"transfer_in\":\n            case \"refund\":\n                return \"text-green-600\";\n            case \"withdrawal\":\n            case \"transfer_out\":\n            case \"purchase\":\n                return \"text-red-600\";\n            default:\n                return \"text-gray-600\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"completed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 16\n                }, this);\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 16\n                }, this);\n            case \"failed\":\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-[400px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 286,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n            lineNumber: 285,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center min-h-[400px] space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"Error Loading Wallet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchWalletData,\n                        className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                        children: \"Try Again\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 296,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n            lineNumber: 295,\n            columnNumber: 7\n        }, this);\n    }\n    if (!wallet) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center min-h-[400px] space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"Wallet Not Found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Your wallet is being set up. Please try again in a moment.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchWalletData,\n                        className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                        children: \"Refresh\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 315,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n            lineNumber: 314,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"My Wallet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Manage your funds and transactions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowDepositModal(true),\n                                        className: \"flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Add Funds\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowTransferModal(true),\n                                        className: \"flex items-center px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Send Money\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-primary-blue to-secondary-blue rounded-xl p-8 text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-blue-100 mb-2\",\n                                            children: \"Available Balance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl font-bold\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)((wallet === null || wallet === void 0 ? void 0 : wallet.balance) || 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-8 w-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-6 w-6 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Total Deposits\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(stats.totalDeposits)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Total Transfers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(stats.totalTransfers)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-6 w-6 text-yellow-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Pending Deposits\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(stats.pendingDeposits)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"-mb-px flex space-x-8\",\n                            children: [\n                                {\n                                    id: \"overview\",\n                                    name: \"Overview\",\n                                    icon: _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                },\n                                {\n                                    id: \"transactions\",\n                                    name: \"Transactions\",\n                                    icon: _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n                                },\n                                {\n                                    id: \"transfers\",\n                                    name: \"Transfers\",\n                                    icon: _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n                                },\n                                {\n                                    id: \"deposits\",\n                                    name: \"Deposits\",\n                                    icon: _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n                                }\n                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(tab.id),\n                                    className: \"flex items-center py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? \"border-primary-blue text-primary-blue\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 17\n                                        }, this),\n                                        tab.name\n                                    ]\n                                }, tab.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                        children: [\n                            activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Recent Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            transactions.slice(0, 5).map((transaction)=>{\n                                                var _transaction_p2p_transfer_receiver, _transaction_p2p_transfer_sender;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                getTransactionIcon(transaction.transaction_type),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900 capitalize\",\n                                                                            children: transaction.transaction_type.replace(\"_\", \" \")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 446,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: transaction.reference_type === \"p2p_transfer\" && transaction.p2p_transfer ? transaction.transaction_type === \"transfer_out\" ? \"Transfer to \".concat(((_transaction_p2p_transfer_receiver = transaction.p2p_transfer.receiver) === null || _transaction_p2p_transfer_receiver === void 0 ? void 0 : _transaction_p2p_transfer_receiver.full_name) || \"Unknown\") : \"Transfer from \".concat(((_transaction_p2p_transfer_sender = transaction.p2p_transfer.sender) === null || _transaction_p2p_transfer_sender === void 0 ? void 0 : _transaction_p2p_transfer_sender.full_name) || \"Unknown\") : transaction.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 449,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        transaction.reference_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400 font-mono\",\n                                                                            children: [\n                                                                                \"Ref: \",\n                                                                                transaction.reference_number\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 461,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium \".concat(getTransactionColor(transaction.transaction_type)),\n                                                                    children: [\n                                                                        transaction.transaction_type.includes(\"in\") || transaction.transaction_type === \"deposit\" || transaction.transaction_type === \"refund\" ? \"+\" : \"-\",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(transaction.amount)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 468,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: new Date(transaction.created_at).toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, transaction.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 19\n                                                }, this);\n                                            }),\n                                            transactions.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"No transactions yet\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"transactions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: \"All Transactions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            transactions.map((transaction)=>{\n                                                var _transaction_p2p_transfer_receiver, _transaction_p2p_transfer_sender;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                getTransactionIcon(transaction.transaction_type),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900 capitalize\",\n                                                                            children: transaction.transaction_type.replace(\"_\", \" \")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 506,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: transaction.reference_type === \"p2p_transfer\" && transaction.p2p_transfer ? transaction.transaction_type === \"transfer_out\" ? \"Transfer to \".concat(((_transaction_p2p_transfer_receiver = transaction.p2p_transfer.receiver) === null || _transaction_p2p_transfer_receiver === void 0 ? void 0 : _transaction_p2p_transfer_receiver.full_name) || \"Unknown\") : \"Transfer from \".concat(((_transaction_p2p_transfer_sender = transaction.p2p_transfer.sender) === null || _transaction_p2p_transfer_sender === void 0 ? void 0 : _transaction_p2p_transfer_sender.full_name) || \"Unknown\") : transaction.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 509,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        transaction.reference_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400 font-mono\",\n                                                                            children: [\n                                                                                \"Ref: \",\n                                                                                transaction.reference_number\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 521,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: new Date(transaction.created_at).toLocaleString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 525,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium mr-2 \".concat(getTransactionColor(transaction.transaction_type)),\n                                                                            children: [\n                                                                                transaction.transaction_type.includes(\"in\") || transaction.transaction_type === \"deposit\" || transaction.transaction_type === \"refund\" ? \"+\" : \"-\",\n                                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(transaction.amount)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 532,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        getStatusIcon(transaction.status)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 531,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        \"Balance: \",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(transaction.balance_after)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 538,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, transaction.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 19\n                                                }, this);\n                                            }),\n                                            transactions.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"No transactions found\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 545,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"transfers\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"P2P Transfers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            transfers.map((transfer)=>{\n                                                var _transfer_receiver, _transfer_sender;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\",\n                                                                    children: transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                        lineNumber: 562,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                        lineNumber: 564,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 560,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                            children: [\n                                                                                transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? \"Sent to\" : \"Received from\",\n                                                                                \" \",\n                                                                                transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? (_transfer_receiver = transfer.receiver) === null || _transfer_receiver === void 0 ? void 0 : _transfer_receiver.full_name : (_transfer_sender = transfer.sender) === null || _transfer_sender === void 0 ? void 0 : _transfer_sender.full_name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 568,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: transfer.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 572,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: new Date(transfer.created_at).toLocaleString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 573,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 567,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium mr-2 \".concat(transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? \"text-red-600\" : \"text-green-600\"),\n                                                                        children: [\n                                                                            transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? \"-\" : \"+\",\n                                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(transfer.amount)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                        lineNumber: 580,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    getStatusIcon(transfer.status)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                lineNumber: 579,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, transfer.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 19\n                                                }, this);\n                                            }),\n                                            transfers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"No transfers found\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 592,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 554,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"deposits\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Deposit Requests\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            depositRequests.map((deposit)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-gray-200 rounded-lg p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                className: \"h-5 w-5 text-green-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                lineNumber: 609,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 608,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"ml-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                                    children: [\n                                                                                        \"Bank Deposit - \",\n                                                                                        deposit.bank_name\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                    lineNumber: 612,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-500\",\n                                                                                    children: [\n                                                                                        deposit.account_holder_name,\n                                                                                        \" (\",\n                                                                                        deposit.account_number,\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                    lineNumber: 615,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 611,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 607,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-lg font-bold text-gray-900\",\n                                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(deposit.amount)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 621,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                getStatusIcon(deposit.status),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"ml-1 text-xs font-medium capitalize \".concat(deposit.status === \"approved\" ? \"text-green-600\" : deposit.status === \"rejected\" ? \"text-red-600\" : \"text-yellow-600\"),\n                                                                                    children: deposit.status\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                    lineNumber: 626,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 624,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 620,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 606,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Submitted: \",\n                                                                        new Date(deposit.created_at).toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 636,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                deposit.transaction_reference && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Reference: \",\n                                                                        deposit.transaction_reference\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 638,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                deposit.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Notes: \",\n                                                                        deposit.notes\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 641,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                deposit.admin_notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Admin Notes: \",\n                                                                        deposit.admin_notes\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 644,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 635,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, deposit.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            depositRequests.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"No deposit requests found\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 601,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 436,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, this),\n            showTransferModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Send Money\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 664,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleTransfer,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Receiver Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 667,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            required: true,\n                                            value: transferForm.receiverEmail,\n                                            onChange: (e)=>setTransferForm((prev)=>({\n                                                        ...prev,\n                                                        receiverEmail: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"Enter receiver's email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 670,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 666,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Amount (Rs)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            required: true,\n                                            min: \"1\",\n                                            max: (wallet === null || wallet === void 0 ? void 0 : wallet.balance) || 0,\n                                            step: \"0.01\",\n                                            value: transferForm.amount,\n                                            onChange: (e)=>setTransferForm((prev)=>({\n                                                        ...prev,\n                                                        amount: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"0.00\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: [\n                                                \"Available: \",\n                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)((wallet === null || wallet === void 0 ? void 0 : wallet.balance) || 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 694,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 679,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Description (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 699,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: transferForm.description,\n                                            onChange: (e)=>setTransferForm((prev)=>({\n                                                        ...prev,\n                                                        description: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"What's this for?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 702,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 698,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowTransferModal(false),\n                                            className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: transferLoading,\n                                            className: \"flex-1 px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 disabled:opacity-50\",\n                                            children: transferLoading ? \"Sending...\" : \"Send Money\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 718,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 710,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 665,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 663,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 662,\n                columnNumber: 9\n            }, this),\n            showDepositModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Request Cash Deposit\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 735,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleDeposit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Amount (Rs) *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 738,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            required: true,\n                                            min: \"1\",\n                                            step: \"0.01\",\n                                            value: depositForm.amount,\n                                            onChange: (e)=>setDepositForm((prev)=>({\n                                                        ...prev,\n                                                        amount: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"0.00\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 741,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 737,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Depositor Name *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 753,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: depositForm.depositor_name,\n                                            onChange: (e)=>setDepositForm((prev)=>({\n                                                        ...prev,\n                                                        depositor_name: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"Name of person who made the deposit\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 756,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 752,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Deposit Receipt/Proof *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 766,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"file\",\n                                            required: true,\n                                            accept: \"image/*,.pdf\",\n                                            onChange: (e)=>{\n                                                var _e_target_files;\n                                                const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                if (file) {\n                                                    // Validate file size (5MB max)\n                                                    if (file.size > 5 * 1024 * 1024) {\n                                                        (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__.showAlert)({\n                                                            title: \"File Too Large\",\n                                                            message: \"Please select a file smaller than 5MB.\",\n                                                            variant: \"warning\"\n                                                        });\n                                                        e.target.value = \"\";\n                                                        return;\n                                                    }\n                                                    setDepositProof(file);\n                                                }\n                                            },\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 769,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: \"Upload bank deposit slip, receipt, or proof of payment (Max 5MB)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 791,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 765,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Notes (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 796,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: depositForm.notes,\n                                            onChange: (e)=>setDepositForm((prev)=>({\n                                                        ...prev,\n                                                        notes: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            rows: 3,\n                                            placeholder: \"Additional information about the deposit\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 799,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 795,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"terms\",\n                                            required: true,\n                                            checked: depositForm.terms_accepted,\n                                            onChange: (e)=>setDepositForm((prev)=>({\n                                                        ...prev,\n                                                        terms_accepted: e.target.checked\n                                                    })),\n                                            className: \"mt-1 h-4 w-4 text-primary-blue focus:ring-primary-blue border-gray-300 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 808,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"terms\",\n                                            className: \"text-sm text-gray-700\",\n                                            children: [\n                                                \"I agree to the\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/terms\",\n                                                    target: \"_blank\",\n                                                    className: \"text-primary-blue hover:underline\",\n                                                    children: \"Terms and Conditions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 818,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                \"and confirm that the deposit information provided is accurate. *\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 816,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 807,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowDepositModal(false),\n                                            className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 825,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: depositLoading,\n                                            className: \"flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50\",\n                                            children: depositLoading ? \"Submitting...\" : \"Submit Request\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 832,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 824,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 736,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 734,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 733,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n        lineNumber: 332,\n        columnNumber: 5\n    }, this);\n}\n_s(WalletPage, \"HDjpKLCdpPq7PZnGhkjHENdYsFU=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = WalletPage;\nvar _c;\n$RefreshReg$(_c, \"WalletPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/wallet/page.tsx\n"));

/***/ })

});