-- Update wallet functions to include reference number generation
-- This migration updates the existing functions to generate and include reference numbers

-- Function to generate transaction reference number in PostgreSQL
CREATE OR REPLACE FUNCTION generate_transaction_reference()
RETURNS varchar(50) AS $$
DECLARE
    date_part varchar(8);
    random_part varchar(8);
    chars varchar(36) := 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    i integer;
    reference_number varchar(50);
    is_unique boolean := false;
BEGIN
    -- Generate date part (YYYYMMDD)
    date_part := to_char(CURRENT_DATE, 'YYYYMMDD');
    
    -- Keep generating until we get a unique reference number
    WHILE NOT is_unique LOOP
        -- Generate random part (8 characters)
        random_part := '';
        FOR i IN 1..8 LOOP
            random_part := random_part || substr(chars, floor(random() * length(chars) + 1)::integer, 1);
        END LOOP;
        
        -- Combine parts
        reference_number := 'TXN-' || date_part || '-' || random_part;
        
        -- Check if this reference number already exists in any table
        SELECT NOT EXISTS (
            SELECT 1 FROM wallet_transactions WHERE reference_number = reference_number
            UNION ALL
            SELECT 1 FROM p2p_transfers WHERE reference_number = reference_number
            UNION ALL
            SELECT 1 FROM deposit_requests WHERE reference_number = reference_number
        ) INTO is_unique;
    END LOOP;
    
    RETURN reference_number;
END;
$$ LANGUAGE plpgsql;

-- Update the wallet balance function to include reference number
CREATE OR REPLACE FUNCTION update_wallet_balance(
    p_wallet_id uuid,
    p_amount decimal(12,2),
    p_transaction_type varchar(20),
    p_description text DEFAULT NULL,
    p_reference_id uuid DEFAULT NULL,
    p_reference_type varchar(50) DEFAULT NULL,
    p_metadata jsonb DEFAULT '{}'
)
RETURNS uuid AS $$
DECLARE
    v_current_balance decimal(12,2);
    v_new_balance decimal(12,2);
    v_transaction_id uuid;
    v_reference_number varchar(50);
BEGIN
    -- Get current balance with row lock
    SELECT balance INTO v_current_balance
    FROM user_wallets
    WHERE id = p_wallet_id
    FOR UPDATE;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Wallet not found';
    END IF;

    -- Calculate new balance based on transaction type
    CASE p_transaction_type
        WHEN 'deposit', 'transfer_in', 'refund' THEN
            v_new_balance := v_current_balance + p_amount;
        WHEN 'withdrawal', 'transfer_out', 'purchase' THEN
            v_new_balance := v_current_balance - p_amount;
            IF v_new_balance < 0 THEN
                RAISE EXCEPTION 'Insufficient balance';
            END IF;
        ELSE
            RAISE EXCEPTION 'Invalid transaction type';
    END CASE;

    -- Update wallet balance
    UPDATE user_wallets
    SET balance = v_new_balance, updated_at = now()
    WHERE id = p_wallet_id;

    -- Generate unique reference number
    v_reference_number := generate_transaction_reference();

    -- Create transaction record with reference number
    INSERT INTO wallet_transactions (
        wallet_id,
        transaction_type,
        amount,
        balance_before,
        balance_after,
        reference_id,
        reference_type,
        description,
        metadata,
        status,
        reference_number
    ) VALUES (
        p_wallet_id,
        p_transaction_type,
        p_amount,
        v_current_balance,
        v_new_balance,
        p_reference_id,
        p_reference_type,
        p_description,
        p_metadata,
        'completed',
        v_reference_number
    ) RETURNING id INTO v_transaction_id;

    RETURN v_transaction_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the P2P transfer function to include reference numbers
CREATE OR REPLACE FUNCTION process_p2p_transfer(
    p_sender_id uuid,
    p_receiver_id uuid,
    p_amount decimal(12,2),
    p_description text DEFAULT NULL
)
RETURNS uuid AS $$
DECLARE
    v_sender_wallet_id uuid;
    v_receiver_wallet_id uuid;
    v_transfer_id uuid;
    v_sender_transaction_id uuid;
    v_receiver_transaction_id uuid;
    v_reference_number varchar(50);
BEGIN
    -- Get wallet IDs
    SELECT id INTO v_sender_wallet_id FROM user_wallets WHERE user_id = p_sender_id;
    SELECT id INTO v_receiver_wallet_id FROM user_wallets WHERE user_id = p_receiver_id;

    IF v_sender_wallet_id IS NULL OR v_receiver_wallet_id IS NULL THEN
        RAISE EXCEPTION 'One or both wallets not found';
    END IF;

    -- Generate unique reference number for the transfer
    v_reference_number := generate_transaction_reference();

    -- Create transfer record with reference number
    INSERT INTO p2p_transfers (sender_id, receiver_id, amount, description, status, reference_number)
    VALUES (p_sender_id, p_receiver_id, p_amount, p_description, 'pending', v_reference_number)
    RETURNING id INTO v_transfer_id;

    -- Process sender transaction (debit)
    SELECT update_wallet_balance(
        v_sender_wallet_id,
        p_amount,
        'transfer_out',
        COALESCE(p_description, 'P2P Transfer to user'),
        v_transfer_id,
        'p2p_transfer'
    ) INTO v_sender_transaction_id;

    -- Process receiver transaction (credit)
    SELECT update_wallet_balance(
        v_receiver_wallet_id,
        p_amount,
        'transfer_in',
        COALESCE(p_description, 'P2P Transfer from user'),
        v_transfer_id,
        'p2p_transfer'
    ) INTO v_receiver_transaction_id;

    -- Update transfer record with transaction IDs and mark as completed
    UPDATE p2p_transfers
    SET 
        sender_transaction_id = v_sender_transaction_id,
        receiver_transaction_id = v_receiver_transaction_id,
        status = 'completed',
        updated_at = now()
    WHERE id = v_transfer_id;

    RETURN v_transfer_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
