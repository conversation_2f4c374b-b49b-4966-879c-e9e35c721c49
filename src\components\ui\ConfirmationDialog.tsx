'use client'

import { useState, useEffect } from 'react'
import { X, AlertTriangle, CheckCircle, XCircle, Info, AlertCircle } from 'lucide-react'
import PremiumButton from './PremiumButton'
import GlassCard, { GlassCardContent } from './GlassCard'

export interface ConfirmationDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  title: string
  message: string
  confirmText?: string
  cancelText?: string
  variant?: 'success' | 'warning' | 'danger' | 'info'
  loading?: boolean
  showIcon?: boolean
}

interface ConfirmationDialogState {
  isOpen: boolean
  onConfirm: () => void
  onClose: () => void
  title: string
  message: string
  confirmText?: string
  cancelText?: string
  variant?: 'success' | 'warning' | 'danger' | 'info'
  loading?: boolean
  showIcon?: boolean
}

// Global state for confirmation dialogs
let globalConfirmationState: ConfirmationDialogState | null = null
let setGlobalConfirmationState: ((state: ConfirmationDialogState | null) => void) | null = null

/**
 * Premium Confirmation Dialog Component
 * Replaces browser-based prompts with a modern, accessible dialog
 */
export default function ConfirmationDialog({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'info',
  loading = false,
  showIcon = true
}: ConfirmationDialogProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true)
      setTimeout(() => setIsAnimating(true), 10)
      // Prevent body scroll
      document.body.style.overflow = 'hidden'
    } else {
      setIsAnimating(false)
      setTimeout(() => {
        setIsVisible(false)
        document.body.style.overflow = 'unset'
      }, 200)
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  const handleConfirm = () => {
    onConfirm()
    if (!loading) {
      onClose()
    }
  }

  const handleCancel = () => {
    if (!loading) {
      onClose()
    }
  }

  const getVariantConfig = () => {
    switch (variant) {
      case 'success':
        return {
          icon: CheckCircle,
          iconColor: 'text-green-600',
          iconBg: 'bg-green-100',
          confirmVariant: 'primary' as const,
          borderColor: 'border-green-200'
        }
      case 'warning':
        return {
          icon: AlertTriangle,
          iconColor: 'text-yellow-600',
          iconBg: 'bg-yellow-100',
          confirmVariant: 'secondary' as const,
          borderColor: 'border-yellow-200'
        }
      case 'danger':
        return {
          icon: XCircle,
          iconColor: 'text-red-600',
          iconBg: 'bg-red-100',
          confirmVariant: 'danger' as const,
          borderColor: 'border-red-200'
        }
      default:
        return {
          icon: Info,
          iconColor: 'text-blue-600',
          iconBg: 'bg-blue-100',
          confirmVariant: 'primary' as const,
          borderColor: 'border-blue-200'
        }
    }
  }

  const config = getVariantConfig()
  const Icon = config.icon

  if (!isVisible) return null

  return (
    <div 
      className={`fixed inset-0 z-50 flex items-center justify-center p-4 transition-all duration-200 ${
        isAnimating ? 'bg-black/60 backdrop-blur-sm' : 'bg-black/0'
      }`}
      onClick={handleCancel}
    >
      <div
        className={`w-full max-w-md transform transition-all duration-200 ${
          isAnimating ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        <GlassCard variant="elevated" className={`border-2 ${config.borderColor}`}>
          <GlassCardContent className="p-6">
            {/* Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center">
                {showIcon && (
                  <div className={`w-10 h-10 ${config.iconBg} rounded-full flex items-center justify-center mr-3 flex-shrink-0`}>
                    <Icon className={`h-5 w-5 ${config.iconColor}`} />
                  </div>
                )}
                <h3 className="text-lg font-semibold text-gray-900 font-heading">
                  {title}
                </h3>
              </div>
              <button
                onClick={handleCancel}
                disabled={loading}
                className="text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-lg hover:bg-gray-100 disabled:opacity-50"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Message */}
            <div className="mb-6">
              <p className="text-gray-600 leading-relaxed">
                {message}
              </p>
            </div>

            {/* Actions */}
            <div className="flex space-x-3">
              <PremiumButton
                variant="ghost"
                onClick={handleCancel}
                disabled={loading}
                className="flex-1"
              >
                {cancelText}
              </PremiumButton>
              <PremiumButton
                variant={config.confirmVariant}
                onClick={handleConfirm}
                loading={loading}
                className="flex-1"
              >
                {confirmText}
              </PremiumButton>
            </div>
          </GlassCardContent>
        </GlassCard>
      </div>
    </div>
  )
}

/**
 * Global Confirmation Dialog Provider
 * Manages a single global confirmation dialog instance
 */
export function GlobalConfirmationDialog() {
  const [state, setState] = useState<ConfirmationDialogState | null>(null)

  useEffect(() => {
    setGlobalConfirmationState = setState
    return () => {
      setGlobalConfirmationState = null
    }
  }, [])

  if (!state) return null

  return (
    <ConfirmationDialog
      isOpen={state.isOpen}
      onClose={state.onClose}
      onConfirm={state.onConfirm}
      title={state.title}
      message={state.message}
      confirmText={state.confirmText}
      cancelText={state.cancelText}
      variant={state.variant}
      loading={state.loading}
      showIcon={state.showIcon}
    />
  )
}

/**
 * Utility function to show confirmation dialog
 * Replaces window.confirm with a premium dialog
 */
export function showConfirmation({
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'info',
  showIcon = true
}: {
  title: string
  message: string
  confirmText?: string
  cancelText?: string
  variant?: 'success' | 'warning' | 'danger' | 'info'
  showIcon?: boolean
}): Promise<boolean> {
  return new Promise((resolve) => {
    if (!setGlobalConfirmationState) {
      // Fallback to browser confirm if provider not available
      resolve(window.confirm(`${title}\n\n${message}`))
      return
    }

    const handleConfirm = () => {
      resolve(true)
      setGlobalConfirmationState!(null)
    }

    const handleClose = () => {
      resolve(false)
      setGlobalConfirmationState!(null)
    }

    setGlobalConfirmationState({
      isOpen: true,
      onConfirm: handleConfirm,
      onClose: handleClose,
      title,
      message,
      confirmText,
      cancelText,
      variant,
      showIcon
    })
  })
}

/**
 * Utility function to show alert dialog
 * Replaces window.alert with a premium dialog
 */
export function showAlert({
  title,
  message,
  confirmText = 'OK',
  variant = 'info',
  showIcon = true
}: {
  title: string
  message: string
  confirmText?: string
  variant?: 'success' | 'warning' | 'danger' | 'info'
  showIcon?: boolean
}): Promise<void> {
  return new Promise((resolve) => {
    if (!setGlobalConfirmationState) {
      // Fallback to browser alert if provider not available
      window.alert(`${title}\n\n${message}`)
      resolve()
      return
    }

    const handleConfirm = () => {
      resolve()
      setGlobalConfirmationState!(null)
    }

    const handleClose = () => {
      resolve()
      setGlobalConfirmationState!(null)
    }

    setGlobalConfirmationState({
      isOpen: true,
      onConfirm: handleConfirm,
      onClose: handleClose,
      title,
      message,
      confirmText,
      cancelText: '', // Hide cancel button for alerts
      variant,
      showIcon
    })
  })
}
