'use client'

import { useState, useEffect, memo, useMemo } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { MapPin, Clock, Eye } from 'lucide-react'
import Card, { CardContent } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { Ad } from '@/types'
import { AdService } from '@/lib/services/ads'
import { formatCurrency } from '@/lib/utils'

// Mock data for featured ads (will be replaced with real data later)
const mockFeaturedAds: Ad[] = [
  {
    id: '1',
    title: '2020 Honda Civic - Excellent Condition',
    description: 'Well maintained Honda Civic with low mileage. Perfect for daily commuting.',
    price: 4500000,
    currency: 'LKR',
    category_id: 'vehicles',
    subcategory_id: 'cars',
    user_id: 'user1',
    location: 'New York, NY',
    condition: 'used',
    status: 'active',
    images: ['/api/placeholder/300/200'],
    featured: true,
    views: 245,
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z',
    expires_at: '2024-02-15T10:00:00Z'
  },
  {
    id: '2',
    title: 'iPhone 14 Pro Max - 256GB',
    description: 'Brand new iPhone 14 Pro Max in Space Black. Still in original packaging.',
    price: 350000,
    currency: 'LKR',
    category_id: 'mobiles',
    subcategory_id: 'mobile-phones',
    user_id: 'user2',
    location: 'Los Angeles, CA',
    condition: 'new',
    status: 'active',
    images: ['/api/placeholder/300/200'],
    featured: true,
    views: 189,
    created_at: '2024-01-14T15:30:00Z',
    updated_at: '2024-01-14T15:30:00Z',
    expires_at: '2024-02-14T15:30:00Z'
  },
  {
    id: '3',
    title: 'Modern 3BR Apartment for Rent',
    description: 'Beautiful modern apartment with city views. Fully furnished with all amenities.',
    price: 2500,
    currency: 'USD',
    category_id: 'property',
    subcategory_id: 'apartment-rentals',
    user_id: 'user3',
    location: 'Chicago, IL',
    condition: 'new',
    status: 'active',
    images: ['/api/placeholder/300/200'],
    featured: true,
    views: 156,
    created_at: '2024-01-13T09:15:00Z',
    updated_at: '2024-01-13T09:15:00Z',
    expires_at: '2024-02-13T09:15:00Z'
  },
  {
    id: '4',
    title: 'MacBook Pro 16" M2 Chip',
    description: 'Latest MacBook Pro with M2 chip. Perfect for professionals and creatives.',
    price: 750000,
    currency: 'LKR',
    category_id: 'electronics',
    subcategory_id: 'computers-tablets',
    user_id: 'user4',
    location: 'San Francisco, CA',
    condition: 'new',
    status: 'active',
    images: ['/api/placeholder/300/200'],
    featured: true,
    views: 203,
    created_at: '2024-01-12T14:20:00Z',
    updated_at: '2024-01-12T14:20:00Z',
    expires_at: '2024-02-12T14:20:00Z'
  }
]

const FeaturedAds = memo(function FeaturedAds() {
  const [featuredAds, setFeaturedAds] = useState<Ad[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchFeaturedAds = async () => {
      try {
        setLoading(true)
        const ads = await AdService.getFeaturedAds(8)
        setFeaturedAds(ads)
      } catch (error) {
        console.error('Error fetching featured ads:', error)
        // Fallback to empty array on error
        setFeaturedAds([])
      } finally {
        setLoading(false)
      }
    }

    fetchFeaturedAds()
  }, [])



  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays}d ago`
    }
  }

  if (loading) {
    return (
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Featured Listings
            </h2>
            <p className="text-xl text-gray-600">
              Discover the best deals from our community
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {Array.from({ length: 4 }).map((_, index) => (
              <Card key={index} className="animate-pulse">
                <div className="w-full h-48 bg-gray-300 rounded-t-lg"></div>
                <CardContent className="p-4">
                  <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-300 rounded w-1/2 mb-4"></div>
                  <div className="h-6 bg-gray-300 rounded w-1/3"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Featured Listings
          </h2>
          <p className="text-xl text-gray-600">
            Discover the best deals from our community
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {featuredAds.map((ad) => (
            <Link key={ad.id} href={`/ad/${ad.id}`}>
              <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer group overflow-hidden">
                <div className="relative">
                  <div className="w-full h-48 bg-gray-200 relative overflow-hidden">
                    {ad.ad_images && ad.ad_images.length > 0 ? (
                      <Image
                        src={ad.ad_images[0].image_url}
                        alt={ad.title}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-200"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-primary-blue/20 to-secondary-blue/20 flex items-center justify-center">
                        <span className="text-gray-500 text-sm">No Image</span>
                      </div>
                    )}
                  </div>
                  <div className="absolute top-2 right-2 bg-accent-orange text-white px-2 py-1 rounded text-xs font-semibold">
                    Featured
                  </div>
                </div>
                <CardContent className="p-4">
                  <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-primary-blue transition-colors">
                    {ad.title}
                  </h3>
                  <div className="flex items-center text-gray-500 text-sm mb-2">
                    <MapPin className="h-4 w-4 mr-1" />
                    {ad.location}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-bold text-primary-blue">
                      {formatCurrency(ad.price)}
                    </span>
                    <div className="flex items-center text-gray-400 text-xs space-x-2">
                      <div className="flex items-center">
                        <Eye className="h-3 w-3 mr-1" />
                        {ad.views}
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        {formatTimeAgo(ad.created_at)}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        <div className="text-center mt-8">
          <Link href="/ads">
            <Button variant="outline" size="lg">
              View All Listings
            </Button>
          </Link>
        </div>
      </div>
    </section>
  )
})

export default FeaturedAds
