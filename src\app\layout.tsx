import type { Metadata } from 'next'
import { Inter, Poppins, Manrope } from 'next/font/google'
import './globals.css'
import { AuthProvider } from '@/contexts/AuthContext'
import { QueryProvider } from '@/components/providers/QueryProvider'
import { measureWebVitals } from '@/lib/performance'
import { initializePerformanceOptimizations } from '@/lib/performanceOptimizations'
import ErrorBoundary from '@/components/ui/ErrorBoundary'
import { GlobalConfirmationDialog } from '@/components/ui/ConfirmationDialog'

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
})

const poppins = Poppins({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  variable: '--font-poppins',
  display: 'swap',
})

const manrope = Manrope({
  subsets: ['latin'],
  variable: '--font-manrope',
  display: 'swap',
})

export const metadata: Metadata = {
  title: 'OKDOI - Premium Marketplace for Everything',
  description: 'Discover amazing deals and sell your items on OKDOI - the premium marketplace for cars, electronics, property, services and more.',
  keywords: 'OKDOI, marketplace, buy, sell, classified ads, cars, electronics, property, services, premium marketplace',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Initialize performance monitoring and optimizations
  if (typeof window !== 'undefined') {
    if (process.env.NODE_ENV === 'development') {
      measureWebVitals()
    }
    initializePerformanceOptimizations()
  }

  return (
    <html lang="en">
      <body className={`${inter.variable} ${poppins.variable} ${manrope.variable} font-sans antialiased`}>
        <ErrorBoundary>
          <QueryProvider>
            <AuthProvider>
              <div className="min-h-screen bg-gray-50">
                {children}
              </div>
              <GlobalConfirmationDialog />
            </AuthProvider>
          </QueryProvider>
        </ErrorBoundary>
      </body>
    </html>
  )
}
