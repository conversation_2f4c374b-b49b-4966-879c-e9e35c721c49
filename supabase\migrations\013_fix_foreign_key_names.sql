-- Fix foreign key constraint names for proper relationship queries

-- Drop existing foreign key constraints and recreate with explicit names

-- P2P Transfers table
ALTER TABLE p2p_transfers 
DROP CONSTRAINT IF EXISTS p2p_transfers_sender_id_fkey,
DROP CONSTRAINT IF EXISTS p2p_transfers_receiver_id_fkey;

ALTER TABLE p2p_transfers 
ADD CONSTRAINT p2p_transfers_sender_id_fkey 
    FOREIGN KEY (sender_id) REFERENCES auth.users(id) ON DELETE CASCADE,
ADD CONSTRAINT p2p_transfers_receiver_id_fkey 
    FOREIGN KEY (receiver_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Deposit Requests table
ALTER TABLE deposit_requests 
DROP CONSTRAINT IF EXISTS deposit_requests_user_id_fkey,
DROP CONSTRAINT IF EXISTS deposit_requests_approved_by_fkey;

ALTER TABLE deposit_requests 
ADD CONSTRAINT deposit_requests_user_id_fkey 
    FOREI<PERSON><PERSON> KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE,
ADD CONSTRAINT deposit_requests_approved_by_fkey 
    FOREIGN KEY (approved_by) REFERENCES auth.users(id);

-- Vendor Shops table
ALTER TABLE vendor_shops
DROP CONSTRAINT IF EXISTS vendor_shops_user_id_fkey;

ALTER TABLE vendor_shops
ADD CONSTRAINT vendor_shops_user_id_fkey
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- Shop Reviews table
ALTER TABLE shop_reviews
DROP CONSTRAINT IF EXISTS shop_reviews_user_id_fkey;

ALTER TABLE shop_reviews
ADD CONSTRAINT shop_reviews_user_id_fkey
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- Shop Followers table
ALTER TABLE shop_followers
DROP CONSTRAINT IF EXISTS shop_followers_user_id_fkey;

ALTER TABLE shop_followers
ADD CONSTRAINT shop_followers_user_id_fkey
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- Shop Reviews table
ALTER TABLE shop_reviews
DROP CONSTRAINT IF EXISTS shop_reviews_user_id_fkey;

ALTER TABLE shop_reviews
ADD CONSTRAINT shop_reviews_user_id_fkey
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- Shop Followers table
ALTER TABLE shop_followers
DROP CONSTRAINT IF EXISTS shop_followers_user_id_fkey;

ALTER TABLE shop_followers
ADD CONSTRAINT shop_followers_user_id_fkey
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- User Wallets table
ALTER TABLE user_wallets 
DROP CONSTRAINT IF EXISTS user_wallets_user_id_fkey;

ALTER TABLE user_wallets 
ADD CONSTRAINT user_wallets_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Wallet Transactions table
ALTER TABLE wallet_transactions 
DROP CONSTRAINT IF EXISTS wallet_transactions_wallet_id_fkey;

ALTER TABLE wallet_transactions 
ADD CONSTRAINT wallet_transactions_wallet_id_fkey 
    FOREIGN KEY (wallet_id) REFERENCES user_wallets(id) ON DELETE CASCADE;

-- Shop Products table
ALTER TABLE shop_products 
DROP CONSTRAINT IF EXISTS shop_products_shop_id_fkey;

ALTER TABLE shop_products 
ADD CONSTRAINT shop_products_shop_id_fkey 
    FOREIGN KEY (shop_id) REFERENCES vendor_shops(id) ON DELETE CASCADE;

-- Shop Reviews table
ALTER TABLE shop_reviews 
DROP CONSTRAINT IF EXISTS shop_reviews_shop_id_fkey,
DROP CONSTRAINT IF EXISTS shop_reviews_user_id_fkey;

ALTER TABLE shop_reviews 
ADD CONSTRAINT shop_reviews_shop_id_fkey 
    FOREIGN KEY (shop_id) REFERENCES vendor_shops(id) ON DELETE CASCADE,
ADD CONSTRAINT shop_reviews_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Shop Followers table
ALTER TABLE shop_followers 
DROP CONSTRAINT IF EXISTS shop_followers_shop_id_fkey,
DROP CONSTRAINT IF EXISTS shop_followers_user_id_fkey;

ALTER TABLE shop_followers 
ADD CONSTRAINT shop_followers_shop_id_fkey 
    FOREIGN KEY (shop_id) REFERENCES vendor_shops(id) ON DELETE CASCADE,
ADD CONSTRAINT shop_followers_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
