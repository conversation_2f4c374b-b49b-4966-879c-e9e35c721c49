"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/deposits/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js":
/*!*********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/eye.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Eye; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst Eye = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Eye\", [\n    [\n        \"path\",\n        {\n            d: \"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z\",\n            key: \"rwhkz3\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"3\",\n            key: \"1v7zrd\"\n        }\n    ]\n]);\n //# sourceMappingURL=eye.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvZXllLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBYU0sTUFBQUEsTUFBTUMsZ0VBQWdCQSxDQUFDLE9BQU87SUFDbEM7UUFDRTtRQUNBO1lBQUVDLEdBQUc7WUFBZ0RDLEtBQUs7UUFBUztLQUNyRTtJQUNBO1FBQUM7UUFBVTtZQUFFQyxJQUFJO1lBQU1DLElBQUk7WUFBTUMsR0FBRztZQUFLSCxLQUFLO1FBQUE7S0FBVTtDQUN6RCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vLi4vc3JjL2ljb25zL2V5ZS50cz84Y2I4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgRXllXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSk5NaUF4TW5NekxUY2dNVEF0TnlBeE1DQTNJREV3SURjdE15QTNMVEV3SURjdE1UQXROeTB4TUMwM1dpSWdMejRLSUNBOFkybHlZMnhsSUdONFBTSXhNaUlnWTNrOUlqRXlJaUJ5UFNJeklpQXZQZ284TDNOMlp6NEspIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2V5ZVxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IEV5ZSA9IGNyZWF0ZUx1Y2lkZUljb24oJ0V5ZScsIFtcbiAgW1xuICAgICdwYXRoJyxcbiAgICB7IGQ6ICdNMiAxMnMzLTcgMTAtNyAxMCA3IDEwIDctMyA3LTEwIDctMTAtNy0xMC03WicsIGtleTogJ3J3aGt6MycgfSxcbiAgXSxcbiAgWydjaXJjbGUnLCB7IGN4OiAnMTInLCBjeTogJzEyJywgcjogJzMnLCBrZXk6ICcxdjd6cmQnIH1dLFxuXSk7XG5cbmV4cG9ydCBkZWZhdWx0IEV5ZTtcbiJdLCJuYW1lcyI6WyJFeWUiLCJjcmVhdGVMdWNpZGVJY29uIiwiZCIsImtleSIsImN4IiwiY3kiLCJyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/admin/deposits/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/admin/deposits/page.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminDepositsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/admin/AdminLayout */ \"(app-pages-browser)/./src/components/admin/AdminLayout.tsx\");\n/* harmony import */ var _lib_services_wallet__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/wallet */ \"(app-pages-browser)/./src/lib/services/wallet.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nfunction DepositActions(param) {\n    let { deposit, onApprove, onReject } = param;\n    _s();\n    const [showMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setShowMenu(!showMenu),\n                className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-1\",\n                    children: deposit.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    onApprove(deposit.id);\n                                    setShowMenu(false);\n                                },\n                                className: \"flex items-center w-full px-4 py-2 text-sm text-green-600 hover:bg-green-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Approve Deposit\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    onReject(deposit.id);\n                                    setShowMenu(false);\n                                },\n                                className: \"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Reject Deposit\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n_s(DepositActions, \"2FjIcsdimgVhm2IsUWodA2ftTZU=\");\n_c = DepositActions;\nfunction AdminDepositsPage() {\n    var _selectedDeposit_user;\n    _s1();\n    const [deposits, setDeposits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalDeposits, setTotalDeposits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const depositsPerPage = 20;\n    // Modal states\n    const [showRejectModal, setShowRejectModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedDeposit, setSelectedDeposit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rejectNotes, setRejectNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchDeposits();\n    }, [\n        currentPage,\n        selectedStatus\n    ]);\n    const fetchDeposits = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const status = selectedStatus === \"all\" ? undefined : selectedStatus;\n            const { requests, total } = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_3__.WalletService.getAllDepositRequests(currentPage, depositsPerPage, status);\n            setDeposits(requests || []);\n            setTotalDeposits(total || 0);\n        } catch (err) {\n            console.error(\"Error fetching deposits:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to load deposits\");\n            setDeposits([]);\n            setTotalDeposits(0);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleApprove = async (depositId)=>{\n        if (!confirm(\"Are you sure you want to approve this deposit?\")) return;\n        try {\n            setActionLoading(true);\n            const { data: { user } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_5__.supabase.auth.getUser();\n            if (!user) throw new Error(\"Not authenticated\");\n            await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_3__.WalletService.approveDepositRequest(depositId, user.id);\n            await fetchDeposits();\n            alert(\"Deposit approved successfully!\");\n        } catch (err) {\n            alert(err instanceof Error ? err.message : \"Failed to approve deposit\");\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleReject = (depositId)=>{\n        const deposit = deposits.find((d)=>d.id === depositId);\n        if (!deposit) return;\n        setSelectedDeposit(deposit);\n        setShowRejectModal(true);\n    };\n    const confirmReject = async ()=>{\n        if (!selectedDeposit || !rejectNotes.trim()) return;\n        try {\n            setActionLoading(true);\n            const { data: { user } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_5__.supabase.auth.getUser();\n            if (!user) throw new Error(\"Not authenticated\");\n            await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_3__.WalletService.rejectDepositRequest(selectedDeposit.id, user.id, rejectNotes);\n            setShowRejectModal(false);\n            setSelectedDeposit(null);\n            setRejectNotes(\"\");\n            await fetchDeposits();\n            alert(\"Deposit rejected successfully!\");\n        } catch (err) {\n            alert(err instanceof Error ? err.message : \"Failed to reject deposit\");\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"approved\":\n                return \"text-green-600 bg-green-100\";\n            case \"pending\":\n                return \"text-yellow-600 bg-yellow-100\";\n            case \"rejected\":\n                return \"text-red-600 bg-red-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"approved\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 16\n                }, this);\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 16\n                }, this);\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const filteredDeposits = deposits.filter((deposit)=>{\n        var _deposit_user_full_name, _deposit_user, _deposit_user_email, _deposit_user1;\n        const matchesSearch = ((_deposit_user = deposit.user) === null || _deposit_user === void 0 ? void 0 : (_deposit_user_full_name = _deposit_user.full_name) === null || _deposit_user_full_name === void 0 ? void 0 : _deposit_user_full_name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_deposit_user1 = deposit.user) === null || _deposit_user1 === void 0 ? void 0 : (_deposit_user_email = _deposit_user1.email) === null || _deposit_user_email === void 0 ? void 0 : _deposit_user_email.toLowerCase().includes(searchTerm.toLowerCase())) || deposit.bank_name.toLowerCase().includes(searchTerm.toLowerCase()) || deposit.account_holder_name.toLowerCase().includes(searchTerm.toLowerCase());\n        return matchesSearch;\n    });\n    const totalPages = Math.ceil(totalDeposits / depositsPerPage);\n    const statusCounts = {\n        all: totalDeposits,\n        pending: deposits.filter((d)=>d.status === \"pending\").length,\n        approved: deposits.filter((d)=>d.status === \"approved\").length,\n        rejected: deposits.filter((d)=>d.status === \"rejected\").length\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Deposit Requests\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Review and manage user deposit requests\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            totalDeposits,\n                                            \" total requests\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                        children: Object.entries(statusCounts).map((param)=>{\n                            let [status, count] = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>setSelectedStatus(status),\n                                className: \"p-4 rounded-lg border cursor-pointer transition-colors \".concat(selectedStatus === status ? \"border-primary-blue bg-primary-blue/5\" : \"border-gray-200 hover:border-gray-300\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: count\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600 capitalize\",\n                                        children: status === \"all\" ? \"Total Requests\" : \"\".concat(status, \" Requests\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, status, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search by user, bank, or account holder...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: fetchDeposits,\n                                    className: \"px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90\",\n                                    children: \"Try Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full divide-y divide-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"User\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Amount\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Depositor & Reference\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Submitted\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-white divide-y divide-gray-200\",\n                                        children: filteredDeposits.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                colSpan: 6,\n                                                className: \"px-6 py-12 text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-12 w-12 text-gray-400 mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                            children: \"No deposit requests found\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500\",\n                                                            children: searchTerm ? \"Try adjusting your search terms.\" : \"No deposit requests have been submitted yet.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 21\n                                        }, this) : filteredDeposits.map((deposit)=>{\n                                            var _deposit_user, _deposit_user1;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-10 w-10 rounded-full bg-primary-blue/10 flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-primary-blue\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                        lineNumber: 318,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                    lineNumber: 317,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                            children: ((_deposit_user = deposit.user) === null || _deposit_user === void 0 ? void 0 : _deposit_user.full_name) || \"No name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                            lineNumber: 321,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: ((_deposit_user1 = deposit.user) === null || _deposit_user1 === void 0 ? void 0 : _deposit_user1.email) || \"No email\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                            lineNumber: 324,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-bold text-gray-900\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(deposit.amount)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-900\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        \"Depositor: \",\n                                                                        deposit.depositor_name || \"Not specified\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                    lineNumber: 335,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                deposit.reference_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-400 font-mono\",\n                                                                    children: [\n                                                                        \"Ref: \",\n                                                                        deposit.reference_number\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                deposit.deposit_slip_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                        href: deposit.deposit_slip_url,\n                                                                        target: \"_blank\",\n                                                                        rel: \"noopener noreferrer\",\n                                                                        className: \"inline-flex items-center text-xs text-blue-600 hover:text-blue-800\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"h-3 w-3 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                                lineNumber: 351,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            \"View Proof\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(getStatusColor(deposit.status)),\n                                                            children: [\n                                                                getStatusIcon(deposit.status),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-1 capitalize\",\n                                                                    children: deposit.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                    lineNumber: 361,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 text-sm text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                        lineNumber: 366,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    new Date(deposit.created_at).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: new Date(deposit.created_at).toLocaleTimeString()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DepositActions, {\n                                                            deposit: deposit,\n                                                            onApprove: handleApprove,\n                                                            onReject: handleReject\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, deposit.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, this),\n                    totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-700\",\n                                children: [\n                                    \"Showing \",\n                                    (currentPage - 1) * depositsPerPage + 1,\n                                    \" to \",\n                                    Math.min(currentPage * depositsPerPage, totalDeposits),\n                                    \" of \",\n                                    totalDeposits,\n                                    \" requests\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setCurrentPage((prev)=>Math.max(prev - 1, 1)),\n                                        disabled: currentPage === 1,\n                                        className: \"px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\",\n                                        children: \"Previous\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 15\n                                    }, this),\n                                    Array.from({\n                                        length: Math.min(5, totalPages)\n                                    }, (_, i)=>{\n                                        const page = i + Math.max(1, currentPage - 2);\n                                        return page <= totalPages ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentPage(page),\n                                            className: \"px-3 py-2 border rounded-lg \".concat(currentPage === page ? \"bg-primary-blue text-white border-primary-blue\" : \"border-gray-300 hover:bg-gray-50\"),\n                                            children: page\n                                        }, page, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 19\n                                        }, this) : null;\n                                    }),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setCurrentPage((prev)=>Math.min(prev + 1, totalPages)),\n                                        disabled: currentPage === totalPages,\n                                        className: \"px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\",\n                                        children: \"Next\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this),\n            showRejectModal && selectedDeposit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Reject Deposit Request\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 437,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mb-2\",\n                                children: [\n                                    \"You are about to reject a deposit request for\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(selectedDeposit.amount)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" \",\n                                    \"from \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: (_selectedDeposit_user = selectedDeposit.user) === null || _selectedDeposit_user === void 0 ? void 0 : _selectedDeposit_user.full_name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 22\n                                    }, this),\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Reason for rejection *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    required: true,\n                                    value: rejectNotes,\n                                    onChange: (e)=>setRejectNotes(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                    rows: 4,\n                                    placeholder: \"Please provide a reason for rejecting this deposit request...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>{\n                                        setShowRejectModal(false);\n                                        setSelectedDeposit(null);\n                                        setRejectNotes(\"\");\n                                    },\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: confirmReject,\n                                    disabled: actionLoading || !rejectNotes.trim(),\n                                    className: \"flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50\",\n                                    children: actionLoading ? \"Rejecting...\" : \"Reject Deposit\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 436,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                lineNumber: 435,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n        lineNumber: 208,\n        columnNumber: 5\n    }, this);\n}\n_s1(AdminDepositsPage, \"uNfBWe0GLu7/FfVZkyvAydfqZ6c=\");\n_c1 = AdminDepositsPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"DepositActions\");\n$RefreshReg$(_c1, \"AdminDepositsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/deposits/page.tsx\n"));

/***/ })

});