"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/wallet/page",{

/***/ "(app-pages-browser)/./src/lib/services/wallet.ts":
/*!************************************!*\
  !*** ./src/lib/services/wallet.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletService: function() { return /* binding */ WalletService; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n/**\n * WalletService - Completely rebuilt to handle Supabase relationships correctly\n *\n * Key fixes:\n * - Uses correct foreign key constraint names from migration files\n * - Robust error handling for all operations\n * - Proper null/empty state handling\n * - Comprehensive logging for debugging\n */ class WalletService {\n    /**\n   * Get user's wallet\n   */ static async getUserWallet(userId) {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USER_WALLETS).select(\"*\").eq(\"user_id\", userId).single();\n        if (error) {\n            if (error.code === \"PGRST116\") {\n                // No wallet found, create one\n                return await this.createUserWallet(userId);\n            }\n            throw new Error(\"Failed to fetch wallet: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Create user wallet\n   */ static async createUserWallet(userId) {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USER_WALLETS).insert({\n            user_id: userId,\n            balance: 0.00,\n            currency: \"LKR\"\n        }).select().single();\n        if (error) {\n            throw new Error(\"Failed to create wallet: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Get wallet transactions with pagination\n   */ static async getWalletTransactions(userId) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, limit = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 20, filters = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : {};\n        const offset = (page - 1) * limit;\n        // First get the user's wallet\n        const wallet = await this.getUserWallet(userId);\n        if (!wallet) {\n            return {\n                transactions: [],\n                total: 0\n            };\n        }\n        let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.WALLET_TRANSACTIONS).select(\"\\n        *,\\n        p2p_transfer:p2p_transfers!wallet_transactions_reference_id_fkey(\\n          id,\\n          sender:users!p2p_transfers_sender_id_fkey(id, full_name, email),\\n          receiver:users!p2p_transfers_receiver_id_fkey(id, full_name, email)\\n        )\\n      \", {\n            count: \"exact\"\n        }).eq(\"wallet_id\", wallet.id).order(\"created_at\", {\n            ascending: false\n        });\n        // Apply filters\n        if (filters.transaction_type) {\n            query = query.eq(\"transaction_type\", filters.transaction_type);\n        }\n        if (filters.status) {\n            query = query.eq(\"status\", filters.status);\n        }\n        if (filters.date_from) {\n            query = query.gte(\"created_at\", filters.date_from);\n        }\n        if (filters.date_to) {\n            query = query.lte(\"created_at\", filters.date_to);\n        }\n        const { data, error, count } = await query.range(offset, offset + limit - 1);\n        if (error) {\n            throw new Error(\"Failed to fetch transactions: \".concat(error.message));\n        }\n        return {\n            transactions: data || [],\n            total: count || 0\n        };\n    }\n    /**\n   * Create P2P transfer\n   */ static async createP2PTransfer(senderId, receiverEmail, amount, description) {\n        // First, find the receiver by email\n        const { data: receiver, error: receiverError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"id, email, full_name\").eq(\"email\", receiverEmail).single();\n        if (receiverError || !receiver) {\n            throw new Error(\"Receiver not found\");\n        }\n        if (receiver.id === senderId) {\n            throw new Error(\"Cannot transfer to yourself\");\n        }\n        // Check sender's balance\n        const senderWallet = await this.getUserWallet(senderId);\n        if (!senderWallet || senderWallet.balance < amount) {\n            throw new Error(\"Insufficient balance\");\n        }\n        // Call the database function to process the transfer\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"process_p2p_transfer\", {\n            p_sender_id: senderId,\n            p_receiver_id: receiver.id,\n            p_amount: amount,\n            p_description: description\n        });\n        if (error) {\n            throw new Error(\"Transfer failed: \".concat(error.message));\n        }\n        // Fetch the created transfer with proper error handling\n        console.log(\"Fetching created transfer with ID: \".concat(data));\n        const { data: transfer, error: transferError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.P2P_TRANSFERS).select(\"\\n        *,\\n        sender:users!p2p_transfers_sender_id_fkey(id, full_name, email),\\n        receiver:users!p2p_transfers_receiver_id_fkey(id, full_name, email)\\n      \").eq(\"id\", data).single();\n        if (transferError) {\n            console.error(\"Error fetching transfer details:\", transferError);\n            throw new Error(\"Failed to fetch transfer details: \".concat(transferError.message));\n        }\n        if (!transfer) {\n            throw new Error(\"Transfer was created but could not be retrieved\");\n        }\n        console.log(\"Transfer created successfully:\", transfer.id);\n        return transfer;\n    }\n    /**\n   * Get user's P2P transfers with proper error handling\n   */ static async getUserTransfers(userId) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, limit = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 20;\n        try {\n            if (!userId) {\n                throw new Error(\"User ID is required\");\n            }\n            const offset = (page - 1) * limit;\n            console.log(\"Fetching P2P transfers for user: \".concat(userId, \", page: \").concat(page, \", limit: \").concat(limit));\n            const { data, error, count } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.P2P_TRANSFERS).select(\"\\n          *,\\n          sender:users!p2p_transfers_sender_id_fkey(id, full_name, email),\\n          receiver:users!p2p_transfers_receiver_id_fkey(id, full_name, email)\\n        \", {\n                count: \"exact\"\n            }).or(\"sender_id.eq.\".concat(userId, \",receiver_id.eq.\").concat(userId)).order(\"created_at\", {\n                ascending: false\n            }).range(offset, offset + limit - 1);\n            if (error) {\n                console.error(\"Error fetching P2P transfers:\", error);\n                throw new Error(\"Failed to fetch transfers: \".concat(error.message));\n            }\n            console.log(\"Successfully fetched \".concat((data === null || data === void 0 ? void 0 : data.length) || 0, \" transfers\"));\n            return {\n                transfers: data || [],\n                total: count || 0\n            };\n        } catch (error) {\n            console.error(\"WalletService.getUserTransfers error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create deposit request with new structure and reference number\n   */ static async createDepositRequest(userId, depositData) {\n        const referenceNumber = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.generateTransactionReference)();\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.DEPOSIT_REQUESTS).insert({\n            user_id: userId,\n            amount: depositData.amount,\n            currency: \"LKR\",\n            depositor_name: depositData.depositor_name,\n            deposit_slip_url: depositData.deposit_slip_url,\n            notes: depositData.notes,\n            reference_number: referenceNumber,\n            status: \"pending\"\n        }).select().single();\n        if (error) {\n            throw new Error(\"Failed to create deposit request: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Get user's deposit requests with comprehensive error handling\n   */ static async getUserDepositRequests(userId) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, limit = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 20;\n        try {\n            if (!userId) {\n                throw new Error(\"User ID is required\");\n            }\n            const offset = (page - 1) * limit;\n            console.log(\"Fetching deposit requests for user: \".concat(userId, \", page: \").concat(page, \", limit: \").concat(limit));\n            const { data, error, count } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.DEPOSIT_REQUESTS).select(\"\\n          *,\\n          user:users!deposit_requests_user_id_fkey(id, full_name, email)\\n        \", {\n                count: \"exact\"\n            }).eq(\"user_id\", userId).order(\"created_at\", {\n                ascending: false\n            }).range(offset, offset + limit - 1);\n            if (error) {\n                console.error(\"Error fetching deposit requests:\", error);\n                throw new Error(\"Failed to fetch deposit requests: \".concat(error.message));\n            }\n            console.log(\"Successfully fetched \".concat((data === null || data === void 0 ? void 0 : data.length) || 0, \" deposit requests\"));\n            return {\n                requests: data || [],\n                total: count || 0\n            };\n        } catch (error) {\n            console.error(\"WalletService.getUserDepositRequests error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all deposit requests (admin only) with comprehensive error handling\n   */ static async getAllDepositRequests() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20, status = arguments.length > 2 ? arguments[2] : void 0;\n        try {\n            const offset = (page - 1) * limit;\n            console.log(\"Fetching all deposit requests - page: \".concat(page, \", limit: \").concat(limit, \", status: \").concat(status || \"all\"));\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.DEPOSIT_REQUESTS).select(\"\\n          *,\\n          user:users!deposit_requests_user_id_fkey(id, full_name, email)\\n        \", {\n                count: \"exact\"\n            }).order(\"created_at\", {\n                ascending: false\n            });\n            if (status && status !== \"all\") {\n                query = query.eq(\"status\", status);\n            }\n            const { data, error, count } = await query.range(offset, offset + limit - 1);\n            if (error) {\n                console.error(\"Error fetching all deposit requests:\", error);\n                throw new Error(\"Failed to fetch deposit requests: \".concat(error.message));\n            }\n            console.log(\"Successfully fetched \".concat((data === null || data === void 0 ? void 0 : data.length) || 0, \" deposit requests (total: \").concat(count || 0, \")\"));\n            return {\n                requests: data || [],\n                total: count || 0\n            };\n        } catch (error) {\n            console.error(\"WalletService.getAllDepositRequests error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create a new deposit request with comprehensive validation\n   */ static async createDepositRequest(userId, requestData) {\n        try {\n            var _requestData_bank_name, _requestData_account_holder_name, _requestData_account_number, _requestData_transaction_reference, _requestData_notes;\n            if (!userId) {\n                throw new Error(\"User ID is required\");\n            }\n            if (!requestData.amount || requestData.amount <= 0) {\n                throw new Error(\"Valid amount is required\");\n            }\n            if (!((_requestData_bank_name = requestData.bank_name) === null || _requestData_bank_name === void 0 ? void 0 : _requestData_bank_name.trim())) {\n                throw new Error(\"Bank name is required\");\n            }\n            if (!((_requestData_account_holder_name = requestData.account_holder_name) === null || _requestData_account_holder_name === void 0 ? void 0 : _requestData_account_holder_name.trim())) {\n                throw new Error(\"Account holder name is required\");\n            }\n            if (!((_requestData_account_number = requestData.account_number) === null || _requestData_account_number === void 0 ? void 0 : _requestData_account_number.trim())) {\n                throw new Error(\"Account number is required\");\n            }\n            console.log(\"Creating deposit request for user: \".concat(userId, \", amount: \").concat(requestData.amount));\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.DEPOSIT_REQUESTS).insert({\n                user_id: userId,\n                amount: requestData.amount,\n                currency: \"LKR\",\n                bank_name: requestData.bank_name.trim(),\n                account_holder_name: requestData.account_holder_name.trim(),\n                account_number: requestData.account_number.trim(),\n                transaction_reference: ((_requestData_transaction_reference = requestData.transaction_reference) === null || _requestData_transaction_reference === void 0 ? void 0 : _requestData_transaction_reference.trim()) || null,\n                notes: ((_requestData_notes = requestData.notes) === null || _requestData_notes === void 0 ? void 0 : _requestData_notes.trim()) || null,\n                status: \"pending\"\n            }).select(\"\\n          *,\\n          user:users!deposit_requests_user_id_fkey(id, full_name, email)\\n        \").single();\n            if (error) {\n                console.error(\"Error creating deposit request:\", error);\n                throw new Error(\"Failed to create deposit request: \".concat(error.message));\n            }\n            if (!data) {\n                throw new Error(\"Deposit request was not created\");\n            }\n            console.log(\"Deposit request created successfully:\", data.id);\n            return data;\n        } catch (error) {\n            console.error(\"WalletService.createDepositRequest error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Approve deposit request (admin only) with comprehensive error handling\n   */ static async approveDepositRequest(requestId, adminId, adminNotes) {\n        try {\n            if (!requestId) {\n                throw new Error(\"Request ID is required\");\n            }\n            if (!adminId) {\n                throw new Error(\"Admin ID is required\");\n            }\n            console.log(\"Approving deposit request: \".concat(requestId, \" by admin: \").concat(adminId));\n            // Get the deposit request\n            const { data: request, error: requestError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.DEPOSIT_REQUESTS).select(\"*\").eq(\"id\", requestId).single();\n            if (requestError) {\n                console.error(\"Error fetching deposit request:\", requestError);\n                throw new Error(\"Deposit request not found: \".concat(requestError.message));\n            }\n            if (!request) {\n                throw new Error(\"Deposit request not found\");\n            }\n            if (request.status !== \"pending\") {\n                throw new Error(\"Deposit request is \".concat(request.status, \", not pending\"));\n            }\n            // Get user's wallet\n            const wallet = await this.getUserWallet(request.user_id);\n            if (!wallet) {\n                throw new Error(\"User wallet not found\");\n            }\n            // Update wallet balance using the database function\n            const { data: transactionId, error: balanceError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"update_wallet_balance\", {\n                p_wallet_id: wallet.id,\n                p_amount: request.amount,\n                p_transaction_type: \"deposit\",\n                p_description: \"Bank deposit - \".concat(request.bank_name),\n                p_reference_id: requestId,\n                p_reference_type: \"deposit_request\"\n            });\n            if (balanceError) {\n                console.error(\"Error updating wallet balance:\", balanceError);\n                throw new Error(\"Failed to update wallet balance: \".concat(balanceError.message));\n            }\n            // Update deposit request status\n            const { error: updateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.DEPOSIT_REQUESTS).update({\n                status: \"approved\",\n                approved_by: adminId,\n                approved_at: new Date().toISOString(),\n                admin_notes: (adminNotes === null || adminNotes === void 0 ? void 0 : adminNotes.trim()) || null,\n                wallet_transaction_id: transactionId,\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", requestId);\n            if (updateError) {\n                console.error(\"Error updating deposit request:\", updateError);\n                throw new Error(\"Failed to approve deposit request: \".concat(updateError.message));\n            }\n            console.log(\"Deposit request \".concat(requestId, \" approved successfully\"));\n        } catch (error) {\n            console.error(\"WalletService.approveDepositRequest error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Reject deposit request (admin only) with comprehensive error handling\n   */ static async rejectDepositRequest(requestId, adminId, adminNotes) {\n        try {\n            if (!requestId) {\n                throw new Error(\"Request ID is required\");\n            }\n            if (!adminId) {\n                throw new Error(\"Admin ID is required\");\n            }\n            if (!(adminNotes === null || adminNotes === void 0 ? void 0 : adminNotes.trim())) {\n                throw new Error(\"Admin notes are required for rejection\");\n            }\n            console.log(\"Rejecting deposit request: \".concat(requestId, \" by admin: \").concat(adminId));\n            // First check if the request exists and is pending\n            const { data: request, error: fetchError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.DEPOSIT_REQUESTS).select(\"id, status\").eq(\"id\", requestId).single();\n            if (fetchError) {\n                console.error(\"Error fetching deposit request:\", fetchError);\n                throw new Error(\"Deposit request not found: \".concat(fetchError.message));\n            }\n            if (!request) {\n                throw new Error(\"Deposit request not found\");\n            }\n            if (request.status !== \"pending\") {\n                throw new Error(\"Deposit request is \".concat(request.status, \", not pending\"));\n            }\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.DEPOSIT_REQUESTS).update({\n                status: \"rejected\",\n                approved_by: adminId,\n                approved_at: new Date().toISOString(),\n                admin_notes: adminNotes.trim(),\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", requestId);\n            if (error) {\n                console.error(\"Error rejecting deposit request:\", error);\n                throw new Error(\"Failed to reject deposit request: \".concat(error.message));\n            }\n            console.log(\"Deposit request \".concat(requestId, \" rejected successfully\"));\n        } catch (error) {\n            console.error(\"WalletService.rejectDepositRequest error:\", error);\n            throw error;\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/wallet.ts\n"));

/***/ })

});