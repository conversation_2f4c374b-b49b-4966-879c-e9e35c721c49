"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/wallet/page",{

/***/ "(app-pages-browser)/./src/lib/services/wallet.ts":
/*!************************************!*\
  !*** ./src/lib/services/wallet.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletService: function() { return /* binding */ WalletService; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n\n/**\n * WalletService - Completely rebuilt to handle Supabase relationships correctly\n *\n * Key fixes:\n * - Uses correct foreign key constraint names from migration files\n * - Robust error handling for all operations\n * - Proper null/empty state handling\n * - Comprehensive logging for debugging\n */ class WalletService {\n    /**\n   * Get user's wallet\n   */ static async getUserWallet(userId) {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USER_WALLETS).select(\"*\").eq(\"user_id\", userId).single();\n        if (error) {\n            if (error.code === \"PGRST116\") {\n                // No wallet found, create one\n                return await this.createUserWallet(userId);\n            }\n            throw new Error(\"Failed to fetch wallet: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Create user wallet\n   */ static async createUserWallet(userId) {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USER_WALLETS).insert({\n            user_id: userId,\n            balance: 0.00,\n            currency: \"LKR\"\n        }).select().single();\n        if (error) {\n            throw new Error(\"Failed to create wallet: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Get wallet transactions with pagination\n   */ static async getWalletTransactions(userId) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, limit = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 20, filters = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : {};\n        const offset = (page - 1) * limit;\n        // First get the user's wallet\n        const wallet = await this.getUserWallet(userId);\n        if (!wallet) {\n            return {\n                transactions: [],\n                total: 0\n            };\n        }\n        let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.WALLET_TRANSACTIONS).select(\"*\", {\n            count: \"exact\"\n        }).eq(\"wallet_id\", wallet.id).order(\"created_at\", {\n            ascending: false\n        });\n        // Apply filters\n        if (filters.transaction_type) {\n            query = query.eq(\"transaction_type\", filters.transaction_type);\n        }\n        if (filters.status) {\n            query = query.eq(\"status\", filters.status);\n        }\n        if (filters.date_from) {\n            query = query.gte(\"created_at\", filters.date_from);\n        }\n        if (filters.date_to) {\n            query = query.lte(\"created_at\", filters.date_to);\n        }\n        const { data, error, count } = await query.range(offset, offset + limit - 1);\n        if (error) {\n            throw new Error(\"Failed to fetch transactions: \".concat(error.message));\n        }\n        return {\n            transactions: data || [],\n            total: count || 0\n        };\n    }\n    /**\n   * Create P2P transfer\n   */ static async createP2PTransfer(senderId, receiverEmail, amount, description) {\n        // First, find the receiver by email\n        const { data: receiver, error: receiverError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"id, email, full_name\").eq(\"email\", receiverEmail).single();\n        if (receiverError || !receiver) {\n            throw new Error(\"Receiver not found\");\n        }\n        if (receiver.id === senderId) {\n            throw new Error(\"Cannot transfer to yourself\");\n        }\n        // Check sender's balance\n        const senderWallet = await this.getUserWallet(senderId);\n        if (!senderWallet || senderWallet.balance < amount) {\n            throw new Error(\"Insufficient balance\");\n        }\n        // Call the database function to process the transfer\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"process_p2p_transfer\", {\n            p_sender_id: senderId,\n            p_receiver_id: receiver.id,\n            p_amount: amount,\n            p_description: description\n        });\n        if (error) {\n            throw new Error(\"Transfer failed: \".concat(error.message));\n        }\n        // Fetch the created transfer with proper error handling\n        console.log(\"Fetching created transfer with ID: \".concat(data));\n        const { data: transfer, error: transferError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.P2P_TRANSFERS).select(\"\\n        *,\\n        sender:users!p2p_transfers_sender_id_fkey(id, full_name, email),\\n        receiver:users!p2p_transfers_receiver_id_fkey(id, full_name, email)\\n      \").eq(\"id\", data).single();\n        if (transferError) {\n            console.error(\"Error fetching transfer details:\", transferError);\n            throw new Error(\"Failed to fetch transfer details: \".concat(transferError.message));\n        }\n        if (!transfer) {\n            throw new Error(\"Transfer was created but could not be retrieved\");\n        }\n        console.log(\"Transfer created successfully:\", transfer.id);\n        return transfer;\n    }\n    /**\n   * Get user's P2P transfers with proper error handling\n   */ static async getUserTransfers(userId) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, limit = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 20;\n        try {\n            if (!userId) {\n                throw new Error(\"User ID is required\");\n            }\n            const offset = (page - 1) * limit;\n            console.log(\"Fetching P2P transfers for user: \".concat(userId, \", page: \").concat(page, \", limit: \").concat(limit));\n            const { data, error, count } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.P2P_TRANSFERS).select(\"\\n          *,\\n          sender:users!p2p_transfers_sender_id_fkey(id, full_name, email),\\n          receiver:users!p2p_transfers_receiver_id_fkey(id, full_name, email)\\n        \", {\n                count: \"exact\"\n            }).or(\"sender_id.eq.\".concat(userId, \",receiver_id.eq.\").concat(userId)).order(\"created_at\", {\n                ascending: false\n            }).range(offset, offset + limit - 1);\n            if (error) {\n                console.error(\"Error fetching P2P transfers:\", error);\n                throw new Error(\"Failed to fetch transfers: \".concat(error.message));\n            }\n            console.log(\"Successfully fetched \".concat((data === null || data === void 0 ? void 0 : data.length) || 0, \" transfers\"));\n            return {\n                transfers: data || [],\n                total: count || 0\n            };\n        } catch (error) {\n            console.error(\"WalletService.getUserTransfers error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create deposit request with new structure\n   */ static async createDepositRequest(userId, depositData) {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.DEPOSIT_REQUESTS).insert({\n            user_id: userId,\n            amount: depositData.amount,\n            currency: \"LKR\",\n            depositor_name: depositData.depositor_name,\n            deposit_slip_url: depositData.deposit_slip_url,\n            notes: depositData.notes,\n            status: \"pending\"\n        }).select().single();\n        if (error) {\n            throw new Error(\"Failed to create deposit request: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Get user's deposit requests with comprehensive error handling\n   */ static async getUserDepositRequests(userId) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, limit = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 20;\n        try {\n            if (!userId) {\n                throw new Error(\"User ID is required\");\n            }\n            const offset = (page - 1) * limit;\n            console.log(\"Fetching deposit requests for user: \".concat(userId, \", page: \").concat(page, \", limit: \").concat(limit));\n            const { data, error, count } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.DEPOSIT_REQUESTS).select(\"\\n          *,\\n          user:users!deposit_requests_user_id_fkey(id, full_name, email)\\n        \", {\n                count: \"exact\"\n            }).eq(\"user_id\", userId).order(\"created_at\", {\n                ascending: false\n            }).range(offset, offset + limit - 1);\n            if (error) {\n                console.error(\"Error fetching deposit requests:\", error);\n                throw new Error(\"Failed to fetch deposit requests: \".concat(error.message));\n            }\n            console.log(\"Successfully fetched \".concat((data === null || data === void 0 ? void 0 : data.length) || 0, \" deposit requests\"));\n            return {\n                requests: data || [],\n                total: count || 0\n            };\n        } catch (error) {\n            console.error(\"WalletService.getUserDepositRequests error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all deposit requests (admin only) with comprehensive error handling\n   */ static async getAllDepositRequests() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20, status = arguments.length > 2 ? arguments[2] : void 0;\n        try {\n            const offset = (page - 1) * limit;\n            console.log(\"Fetching all deposit requests - page: \".concat(page, \", limit: \").concat(limit, \", status: \").concat(status || \"all\"));\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.DEPOSIT_REQUESTS).select(\"\\n          *,\\n          user:users!deposit_requests_user_id_fkey(id, full_name, email)\\n        \", {\n                count: \"exact\"\n            }).order(\"created_at\", {\n                ascending: false\n            });\n            if (status && status !== \"all\") {\n                query = query.eq(\"status\", status);\n            }\n            const { data, error, count } = await query.range(offset, offset + limit - 1);\n            if (error) {\n                console.error(\"Error fetching all deposit requests:\", error);\n                throw new Error(\"Failed to fetch deposit requests: \".concat(error.message));\n            }\n            console.log(\"Successfully fetched \".concat((data === null || data === void 0 ? void 0 : data.length) || 0, \" deposit requests (total: \").concat(count || 0, \")\"));\n            return {\n                requests: data || [],\n                total: count || 0\n            };\n        } catch (error) {\n            console.error(\"WalletService.getAllDepositRequests error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create a new deposit request with comprehensive validation\n   */ static async createDepositRequest(userId, requestData) {\n        try {\n            var _requestData_bank_name, _requestData_account_holder_name, _requestData_account_number, _requestData_transaction_reference, _requestData_notes;\n            if (!userId) {\n                throw new Error(\"User ID is required\");\n            }\n            if (!requestData.amount || requestData.amount <= 0) {\n                throw new Error(\"Valid amount is required\");\n            }\n            if (!((_requestData_bank_name = requestData.bank_name) === null || _requestData_bank_name === void 0 ? void 0 : _requestData_bank_name.trim())) {\n                throw new Error(\"Bank name is required\");\n            }\n            if (!((_requestData_account_holder_name = requestData.account_holder_name) === null || _requestData_account_holder_name === void 0 ? void 0 : _requestData_account_holder_name.trim())) {\n                throw new Error(\"Account holder name is required\");\n            }\n            if (!((_requestData_account_number = requestData.account_number) === null || _requestData_account_number === void 0 ? void 0 : _requestData_account_number.trim())) {\n                throw new Error(\"Account number is required\");\n            }\n            console.log(\"Creating deposit request for user: \".concat(userId, \", amount: \").concat(requestData.amount));\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.DEPOSIT_REQUESTS).insert({\n                user_id: userId,\n                amount: requestData.amount,\n                currency: \"LKR\",\n                bank_name: requestData.bank_name.trim(),\n                account_holder_name: requestData.account_holder_name.trim(),\n                account_number: requestData.account_number.trim(),\n                transaction_reference: ((_requestData_transaction_reference = requestData.transaction_reference) === null || _requestData_transaction_reference === void 0 ? void 0 : _requestData_transaction_reference.trim()) || null,\n                notes: ((_requestData_notes = requestData.notes) === null || _requestData_notes === void 0 ? void 0 : _requestData_notes.trim()) || null,\n                status: \"pending\"\n            }).select(\"\\n          *,\\n          user:users!deposit_requests_user_id_fkey(id, full_name, email)\\n        \").single();\n            if (error) {\n                console.error(\"Error creating deposit request:\", error);\n                throw new Error(\"Failed to create deposit request: \".concat(error.message));\n            }\n            if (!data) {\n                throw new Error(\"Deposit request was not created\");\n            }\n            console.log(\"Deposit request created successfully:\", data.id);\n            return data;\n        } catch (error) {\n            console.error(\"WalletService.createDepositRequest error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Approve deposit request (admin only) with comprehensive error handling\n   */ static async approveDepositRequest(requestId, adminId, adminNotes) {\n        try {\n            if (!requestId) {\n                throw new Error(\"Request ID is required\");\n            }\n            if (!adminId) {\n                throw new Error(\"Admin ID is required\");\n            }\n            console.log(\"Approving deposit request: \".concat(requestId, \" by admin: \").concat(adminId));\n            // Get the deposit request\n            const { data: request, error: requestError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.DEPOSIT_REQUESTS).select(\"*\").eq(\"id\", requestId).single();\n            if (requestError) {\n                console.error(\"Error fetching deposit request:\", requestError);\n                throw new Error(\"Deposit request not found: \".concat(requestError.message));\n            }\n            if (!request) {\n                throw new Error(\"Deposit request not found\");\n            }\n            if (request.status !== \"pending\") {\n                throw new Error(\"Deposit request is \".concat(request.status, \", not pending\"));\n            }\n            // Get user's wallet\n            const wallet = await this.getUserWallet(request.user_id);\n            if (!wallet) {\n                throw new Error(\"User wallet not found\");\n            }\n            // Update wallet balance using the database function\n            const { data: transactionId, error: balanceError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"update_wallet_balance\", {\n                p_wallet_id: wallet.id,\n                p_amount: request.amount,\n                p_transaction_type: \"deposit\",\n                p_description: \"Bank deposit - \".concat(request.bank_name),\n                p_reference_id: requestId,\n                p_reference_type: \"deposit_request\"\n            });\n            if (balanceError) {\n                console.error(\"Error updating wallet balance:\", balanceError);\n                throw new Error(\"Failed to update wallet balance: \".concat(balanceError.message));\n            }\n            // Update deposit request status\n            const { error: updateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.DEPOSIT_REQUESTS).update({\n                status: \"approved\",\n                approved_by: adminId,\n                approved_at: new Date().toISOString(),\n                admin_notes: (adminNotes === null || adminNotes === void 0 ? void 0 : adminNotes.trim()) || null,\n                wallet_transaction_id: transactionId,\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", requestId);\n            if (updateError) {\n                console.error(\"Error updating deposit request:\", updateError);\n                throw new Error(\"Failed to approve deposit request: \".concat(updateError.message));\n            }\n            console.log(\"Deposit request \".concat(requestId, \" approved successfully\"));\n        } catch (error) {\n            console.error(\"WalletService.approveDepositRequest error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Reject deposit request (admin only) with comprehensive error handling\n   */ static async rejectDepositRequest(requestId, adminId, adminNotes) {\n        try {\n            if (!requestId) {\n                throw new Error(\"Request ID is required\");\n            }\n            if (!adminId) {\n                throw new Error(\"Admin ID is required\");\n            }\n            if (!(adminNotes === null || adminNotes === void 0 ? void 0 : adminNotes.trim())) {\n                throw new Error(\"Admin notes are required for rejection\");\n            }\n            console.log(\"Rejecting deposit request: \".concat(requestId, \" by admin: \").concat(adminId));\n            // First check if the request exists and is pending\n            const { data: request, error: fetchError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.DEPOSIT_REQUESTS).select(\"id, status\").eq(\"id\", requestId).single();\n            if (fetchError) {\n                console.error(\"Error fetching deposit request:\", fetchError);\n                throw new Error(\"Deposit request not found: \".concat(fetchError.message));\n            }\n            if (!request) {\n                throw new Error(\"Deposit request not found\");\n            }\n            if (request.status !== \"pending\") {\n                throw new Error(\"Deposit request is \".concat(request.status, \", not pending\"));\n            }\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.DEPOSIT_REQUESTS).update({\n                status: \"rejected\",\n                approved_by: adminId,\n                approved_at: new Date().toISOString(),\n                admin_notes: adminNotes.trim(),\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", requestId);\n            if (error) {\n                console.error(\"Error rejecting deposit request:\", error);\n                throw new Error(\"Failed to reject deposit request: \".concat(error.message));\n            }\n            console.log(\"Deposit request \".concat(requestId, \" rejected successfully\"));\n        } catch (error) {\n            console.error(\"WalletService.rejectDepositRequest error:\", error);\n            throw error;\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/wallet.ts\n"));

/***/ })

});