"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/wallet/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/wallet/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/wallet/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ WalletPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,Banknote,CheckCircle,Clock,CreditCard,Filter,Plus,Search,Send,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/DashboardLayout */ \"(app-pages-browser)/./src/components/dashboard/DashboardLayout.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_services_wallet__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/services/wallet */ \"(app-pages-browser)/./src/lib/services/wallet.ts\");\n/* harmony import */ var _lib_services_storage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/services/storage */ \"(app-pages-browser)/./src/lib/services/storage.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(app-pages-browser)/./src/components/ui/LoadingSpinner.tsx\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction WalletPage() {\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [wallet, setWallet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [transactions, setTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [transfers, setTransfers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [depositRequests, setDepositRequests] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalBalance: 0,\n        totalDeposits: 0,\n        totalTransfers: 0,\n        pendingDeposits: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    // Modal states\n    const [showTransferModal, setShowTransferModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDepositModal, setShowDepositModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Transfer form\n    const [transferForm, setTransferForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        receiverEmail: \"\",\n        amount: \"\",\n        description: \"\"\n    });\n    const [transferLoading, setTransferLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Deposit form\n    const [depositForm, setDepositForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        amount: \"\",\n        depositor_name: \"\",\n        notes: \"\",\n        terms_accepted: false\n    });\n    const [depositProof, setDepositProof] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [depositLoading, setDepositLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            fetchWalletData();\n        }\n    }, [\n        user\n    ]);\n    const fetchWalletData = async ()=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            setError(null);\n            console.log(\"Fetching wallet data for user:\", user.id);\n            // Fetch wallet with error handling\n            const walletData = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_4__.WalletService.getUserWallet(user.id);\n            setWallet(walletData);\n            console.log(\"Wallet data fetched:\", walletData);\n            // Fetch recent transactions with error handling\n            const { transactions: transactionsData } = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_4__.WalletService.getWalletTransactions(user.id, 1, 10);\n            setTransactions(transactionsData || []);\n            console.log(\"Transactions fetched:\", (transactionsData === null || transactionsData === void 0 ? void 0 : transactionsData.length) || 0);\n            // Fetch recent transfers with error handling\n            const { transfers: transfersData } = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_4__.WalletService.getUserTransfers(user.id, 1, 10);\n            setTransfers(transfersData || []);\n            console.log(\"Transfers fetched:\", (transfersData === null || transfersData === void 0 ? void 0 : transfersData.length) || 0);\n            // Fetch deposit requests with error handling\n            const { requests: depositsData } = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_4__.WalletService.getUserDepositRequests(user.id, 1, 10);\n            setDepositRequests(depositsData || []);\n            console.log(\"Deposit requests fetched:\", (depositsData === null || depositsData === void 0 ? void 0 : depositsData.length) || 0);\n            // Calculate stats with null safety\n            const totalDeposits = (transactionsData || []).filter((t)=>t.transaction_type === \"deposit\" && t.status === \"completed\").reduce((sum, t)=>sum + (t.amount || 0), 0);\n            const totalTransfers = (transfersData || []).filter((t)=>t.sender_id === user.id && t.status === \"completed\").reduce((sum, t)=>sum + (t.amount || 0), 0);\n            const pendingDeposits = (depositsData || []).filter((d)=>d.status === \"pending\").reduce((sum, d)=>sum + (d.amount || 0), 0);\n            setStats({\n                totalBalance: (walletData === null || walletData === void 0 ? void 0 : walletData.balance) || 0,\n                totalDeposits,\n                totalTransfers,\n                pendingDeposits\n            });\n            console.log(\"Wallet data fetch completed successfully\");\n        } catch (err) {\n            console.error(\"Error fetching wallet data:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to load wallet data\");\n            // Reset data on error\n            setWallet(null);\n            setTransactions([]);\n            setTransfers([]);\n            setDepositRequests([]);\n            setStats({\n                totalBalance: 0,\n                totalDeposits: 0,\n                totalTransfers: 0,\n                pendingDeposits: 0\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleTransfer = async (e)=>{\n        e.preventDefault();\n        if (!user) return;\n        try {\n            setTransferLoading(true);\n            await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_4__.WalletService.createP2PTransfer(user.id, transferForm.receiverEmail, parseFloat(transferForm.amount), transferForm.description || undefined);\n            setShowTransferModal(false);\n            setTransferForm({\n                receiverEmail: \"\",\n                amount: \"\",\n                description: \"\"\n            });\n            await fetchWalletData();\n            alert(\"Transfer completed successfully!\");\n        } catch (err) {\n            alert(err instanceof Error ? err.message : \"Transfer failed\");\n        } finally{\n            setTransferLoading(false);\n        }\n    };\n    const handleDeposit = async (e)=>{\n        e.preventDefault();\n        if (!user) return;\n        // Validation\n        if (!depositForm.terms_accepted) {\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__.showAlert)({\n                title: \"Terms Required\",\n                message: \"Please accept the terms and conditions to proceed.\",\n                variant: \"warning\"\n            });\n            return;\n        }\n        if (!depositProof) {\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__.showAlert)({\n                title: \"Proof Required\",\n                message: \"Please upload a deposit receipt or proof of payment.\",\n                variant: \"warning\"\n            });\n            return;\n        }\n        try {\n            setDepositLoading(true);\n            // Upload proof file first\n            let proofUrl = \"\";\n            if (depositProof) {\n                proofUrl = await _lib_services_storage__WEBPACK_IMPORTED_MODULE_5__.StorageService.uploadImage(depositProof, user.id);\n            }\n            await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_4__.WalletService.createDepositRequest(user.id, {\n                amount: parseFloat(depositForm.amount),\n                depositor_name: depositForm.depositor_name,\n                notes: depositForm.notes || undefined,\n                deposit_slip_url: proofUrl\n            });\n            setShowDepositModal(false);\n            setDepositForm({\n                amount: \"\",\n                depositor_name: \"\",\n                notes: \"\",\n                terms_accepted: false\n            });\n            setDepositProof(null);\n            await fetchWalletData();\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__.showAlert)({\n                title: \"Success\",\n                message: \"Deposit request submitted successfully! We will review it shortly.\",\n                variant: \"success\"\n            });\n        } catch (err) {\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__.showAlert)({\n                title: \"Error\",\n                message: err instanceof Error ? err.message : \"Failed to submit deposit request\",\n                variant: \"danger\"\n            });\n        } finally{\n            setDepositLoading(false);\n        }\n    };\n    const getTransactionIcon = (type)=>{\n        switch(type){\n            case \"deposit\":\n            case \"transfer_in\":\n            case \"refund\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 16\n                }, this);\n            case \"withdrawal\":\n            case \"transfer_out\":\n            case \"purchase\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getTransactionColor = (type)=>{\n        switch(type){\n            case \"deposit\":\n            case \"transfer_in\":\n            case \"refund\":\n                return \"text-green-600\";\n            case \"withdrawal\":\n            case \"transfer_out\":\n            case \"purchase\":\n                return \"text-red-600\";\n            default:\n                return \"text-gray-600\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"completed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 16\n                }, this);\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 16\n                }, this);\n            case \"failed\":\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-[400px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 286,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n            lineNumber: 285,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center min-h-[400px] space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"Error Loading Wallet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchWalletData,\n                        className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                        children: \"Try Again\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 296,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n            lineNumber: 295,\n            columnNumber: 7\n        }, this);\n    }\n    if (!wallet) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center min-h-[400px] space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"Wallet Not Found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Your wallet is being set up. Please try again in a moment.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchWalletData,\n                        className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                        children: \"Refresh\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 315,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n            lineNumber: 314,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"My Wallet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Manage your funds and transactions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowDepositModal(true),\n                                        className: \"flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Add Funds\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowTransferModal(true),\n                                        className: \"flex items-center px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Send Money\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-primary-blue to-secondary-blue rounded-xl p-8 text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-blue-100 mb-2\",\n                                            children: \"Available Balance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl font-bold\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)((wallet === null || wallet === void 0 ? void 0 : wallet.balance) || 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-8 w-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-6 w-6 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Total Deposits\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(stats.totalDeposits)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Total Transfers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(stats.totalTransfers)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-6 w-6 text-yellow-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Pending Deposits\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(stats.pendingDeposits)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"-mb-px flex space-x-8\",\n                            children: [\n                                {\n                                    id: \"overview\",\n                                    name: \"Overview\",\n                                    icon: _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                },\n                                {\n                                    id: \"transactions\",\n                                    name: \"Transactions\",\n                                    icon: _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n                                },\n                                {\n                                    id: \"transfers\",\n                                    name: \"Transfers\",\n                                    icon: _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n                                },\n                                {\n                                    id: \"deposits\",\n                                    name: \"Deposits\",\n                                    icon: _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n                                }\n                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(tab.id),\n                                    className: \"flex items-center py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? \"border-primary-blue text-primary-blue\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 17\n                                        }, this),\n                                        tab.name\n                                    ]\n                                }, tab.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                        children: [\n                            activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Recent Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            transactions.slice(0, 5).map((transaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                getTransactionIcon(transaction.transaction_type),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900 capitalize\",\n                                                                            children: transaction.transaction_type.replace(\"_\", \" \")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 446,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: transaction.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 449,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium \".concat(getTransactionColor(transaction.transaction_type)),\n                                                                    children: [\n                                                                        transaction.transaction_type.includes(\"in\") || transaction.transaction_type === \"deposit\" || transaction.transaction_type === \"refund\" ? \"+\" : \"-\",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(transaction.amount)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 453,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: new Date(transaction.created_at).toLocaleDateString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 457,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, transaction.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            transactions.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"No transactions yet\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"transactions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: \"All Transactions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            transactions.map((transaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                getTransactionIcon(transaction.transaction_type),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900 capitalize\",\n                                                                            children: transaction.transaction_type.replace(\"_\", \" \")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 491,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: transaction.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 494,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: new Date(transaction.created_at).toLocaleString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 495,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium mr-2 \".concat(getTransactionColor(transaction.transaction_type)),\n                                                                            children: [\n                                                                                transaction.transaction_type.includes(\"in\") || transaction.transaction_type === \"deposit\" || transaction.transaction_type === \"refund\" ? \"+\" : \"-\",\n                                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(transaction.amount)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 502,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        getStatusIcon(transaction.status)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 501,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        \"Balance: \",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(transaction.balance_after)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 508,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, transaction.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            transactions.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"No transactions found\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"transfers\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"P2P Transfers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            transfers.map((transfer)=>{\n                                                var _transfer_receiver, _transfer_sender;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\",\n                                                                    children: transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                        lineNumber: 532,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                        lineNumber: 534,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 530,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                            children: [\n                                                                                transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? \"Sent to\" : \"Received from\",\n                                                                                \" \",\n                                                                                transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? (_transfer_receiver = transfer.receiver) === null || _transfer_receiver === void 0 ? void 0 : _transfer_receiver.full_name : (_transfer_sender = transfer.sender) === null || _transfer_sender === void 0 ? void 0 : _transfer_sender.full_name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 538,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: transfer.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 542,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: new Date(transfer.created_at).toLocaleString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 543,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 537,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium mr-2 \".concat(transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? \"text-red-600\" : \"text-green-600\"),\n                                                                        children: [\n                                                                            transfer.sender_id === (user === null || user === void 0 ? void 0 : user.id) ? \"-\" : \"+\",\n                                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(transfer.amount)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                        lineNumber: 550,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    getStatusIcon(transfer.status)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                lineNumber: 549,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, transfer.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 19\n                                                }, this);\n                                            }),\n                                            transfers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"No transfers found\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 524,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"deposits\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Deposit Requests\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            depositRequests.map((deposit)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-gray-200 rounded-lg p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_Banknote_CheckCircle_Clock_CreditCard_Filter_Plus_Search_Send_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                className: \"h-5 w-5 text-green-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                lineNumber: 579,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 578,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"ml-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                                    children: [\n                                                                                        \"Bank Deposit - \",\n                                                                                        deposit.bank_name\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                    lineNumber: 582,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-500\",\n                                                                                    children: [\n                                                                                        deposit.account_holder_name,\n                                                                                        \" (\",\n                                                                                        deposit.account_number,\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                    lineNumber: 585,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 581,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 577,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-lg font-bold text-gray-900\",\n                                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(deposit.amount)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 591,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                getStatusIcon(deposit.status),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"ml-1 text-xs font-medium capitalize \".concat(deposit.status === \"approved\" ? \"text-green-600\" : deposit.status === \"rejected\" ? \"text-red-600\" : \"text-yellow-600\"),\n                                                                                    children: deposit.status\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                                    lineNumber: 596,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                            lineNumber: 594,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 590,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 576,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Submitted: \",\n                                                                        new Date(deposit.created_at).toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 606,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                deposit.transaction_reference && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Reference: \",\n                                                                        deposit.transaction_reference\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 608,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                deposit.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Notes: \",\n                                                                        deposit.notes\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 611,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                deposit.admin_notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"Admin Notes: \",\n                                                                        deposit.admin_notes\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                                    lineNumber: 614,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                            lineNumber: 605,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, deposit.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            depositRequests.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"No deposit requests found\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 573,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                        lineNumber: 436,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, this),\n            showTransferModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Send Money\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 634,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleTransfer,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Receiver Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 637,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            required: true,\n                                            value: transferForm.receiverEmail,\n                                            onChange: (e)=>setTransferForm((prev)=>({\n                                                        ...prev,\n                                                        receiverEmail: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"Enter receiver's email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 636,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Amount (Rs)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 650,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            required: true,\n                                            min: \"1\",\n                                            max: (wallet === null || wallet === void 0 ? void 0 : wallet.balance) || 0,\n                                            step: \"0.01\",\n                                            value: transferForm.amount,\n                                            onChange: (e)=>setTransferForm((prev)=>({\n                                                        ...prev,\n                                                        amount: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"0.00\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: [\n                                                \"Available: \",\n                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)((wallet === null || wallet === void 0 ? void 0 : wallet.balance) || 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 649,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Description (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 669,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: transferForm.description,\n                                            onChange: (e)=>setTransferForm((prev)=>({\n                                                        ...prev,\n                                                        description: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"What's this for?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 668,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowTransferModal(false),\n                                            className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 681,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: transferLoading,\n                                            className: \"flex-1 px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 disabled:opacity-50\",\n                                            children: transferLoading ? \"Sending...\" : \"Send Money\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 680,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 635,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 633,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 632,\n                columnNumber: 9\n            }, this),\n            showDepositModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Request Cash Deposit\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 705,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleDeposit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Amount (Rs) *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 708,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            required: true,\n                                            min: \"1\",\n                                            step: \"0.01\",\n                                            value: depositForm.amount,\n                                            onChange: (e)=>setDepositForm((prev)=>({\n                                                        ...prev,\n                                                        amount: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"0.00\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 707,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Depositor Name *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: depositForm.depositor_name,\n                                            onChange: (e)=>setDepositForm((prev)=>({\n                                                        ...prev,\n                                                        depositor_name: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            placeholder: \"Name of person who made the deposit\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 726,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 722,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Deposit Receipt/Proof *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 736,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"file\",\n                                            required: true,\n                                            accept: \"image/*,.pdf\",\n                                            onChange: (e)=>{\n                                                var _e_target_files;\n                                                const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                if (file) {\n                                                    // Validate file size (5MB max)\n                                                    if (file.size > 5 * 1024 * 1024) {\n                                                        (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__.showAlert)({\n                                                            title: \"File Too Large\",\n                                                            message: \"Please select a file smaller than 5MB.\",\n                                                            variant: \"warning\"\n                                                        });\n                                                        e.target.value = \"\";\n                                                        return;\n                                                    }\n                                                    setDepositProof(file);\n                                                }\n                                            },\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 739,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: \"Upload bank deposit slip, receipt, or proof of payment (Max 5MB)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 761,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 735,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Notes (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 766,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: depositForm.notes,\n                                            onChange: (e)=>setDepositForm((prev)=>({\n                                                        ...prev,\n                                                        notes: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                            rows: 3,\n                                            placeholder: \"Additional information about the deposit\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 769,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 765,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"terms\",\n                                            required: true,\n                                            checked: depositForm.terms_accepted,\n                                            onChange: (e)=>setDepositForm((prev)=>({\n                                                        ...prev,\n                                                        terms_accepted: e.target.checked\n                                                    })),\n                                            className: \"mt-1 h-4 w-4 text-primary-blue focus:ring-primary-blue border-gray-300 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 778,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"terms\",\n                                            className: \"text-sm text-gray-700\",\n                                            children: [\n                                                \"I agree to the\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/terms\",\n                                                    target: \"_blank\",\n                                                    className: \"text-primary-blue hover:underline\",\n                                                    children: \"Terms and Conditions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 788,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                \"and confirm that the deposit information provided is accurate. *\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 786,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 777,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowDepositModal(false),\n                                            className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 795,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: depositLoading,\n                                            className: \"flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50\",\n                                            children: depositLoading ? \"Submitting...\" : \"Submit Request\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 802,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 794,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                            lineNumber: 706,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                    lineNumber: 704,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n                lineNumber: 703,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\dashboard\\\\wallet\\\\page.tsx\",\n        lineNumber: 332,\n        columnNumber: 5\n    }, this);\n}\n_s(WalletPage, \"HDjpKLCdpPq7PZnGhkjHENdYsFU=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = WalletPage;\nvar _c;\n$RefreshReg$(_c, \"WalletPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/wallet/page.tsx\n"));

/***/ })

});