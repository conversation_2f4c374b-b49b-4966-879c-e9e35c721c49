'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import {
  ArrowLeft,
  MapPin,
  Calendar,
  Eye,
  Heart,
  Share2,
  Phone,
  MessageCircle,
  Star,
  Shield,
  ChevronLeft,
  ChevronRight,
  User
} from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import AdSlider from '@/components/ads/AdSlider'
import ChatModal from '@/components/ChatModal'
import { AdService } from '@/lib/services/ads'
import { AdWithDetails } from '@/types'
import { formatCurrency, formatDate } from '@/lib/utils'
import { useAuth } from '@/contexts/AuthContext'

export default function SingleAdPage() {
  const params = useParams()
  const router = useRouter()
  const { user } = useAuth()
  const [ad, setAd] = useState<AdWithDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [isFavorite, setIsFavorite] = useState(false)
  const [memberAds, setMemberAds] = useState<AdWithDetails[]>([])
  const [similarAds, setSimilarAds] = useState<AdWithDetails[]>([])
  const [relatedLoading, setRelatedLoading] = useState(false)
  const [isChatModalOpen, setIsChatModalOpen] = useState(false)

  // Check if the current user is the owner of this ad
  const isOwnAd = user && ad && user.id === ad.user_id

  useEffect(() => {
    if (params.id) {
      fetchAd(params.id as string)
    }
  }, [params.id])

  const fetchAd = async (id: string) => {
    try {
      setLoading(true)
      const adData = await AdService.getAdById(id)
      
      if (!adData) {
        setError('Ad not found')
        return
      }

      setAd(adData)

      // Fetch related ads after getting the main ad
      await fetchRelatedAds(adData)
    } catch (error) {
      console.error('Error fetching ad:', error)
      setError('Failed to load ad')
    } finally {
      setLoading(false)
    }
  }

  const fetchRelatedAds = async (currentAd: AdWithDetails) => {
    try {
      setRelatedLoading(true)

      // Fetch more ads from the same member (excluding current ad)
      const memberAdsResult = await AdService.getAds(
        { userId: currentAd.user_id, status: 'active' },
        1,
        8
      )
      const filteredMemberAds = memberAdsResult.ads.filter(memberAd => memberAd.id !== currentAd.id)
      setMemberAds(filteredMemberAds)

      // Fetch similar ads from the same subcategory (excluding current ad and member ads)
      const similarAdsResult = await AdService.getAds(
        { subcategory_id: currentAd.subcategory_id, status: 'active' },
        1,
        8
      )
      const filteredSimilarAds = similarAdsResult.ads.filter(
        similarAd => similarAd.id !== currentAd.id && similarAd.user_id !== currentAd.user_id
      )
      setSimilarAds(filteredSimilarAds)

    } catch (error) {
      console.error('Error fetching related ads:', error)
    } finally {
      setRelatedLoading(false)
    }
  }

  const handlePrevImage = () => {
    if (ad?.ad_images && ad.ad_images.length > 0) {
      setCurrentImageIndex((prev) =>
        prev === 0 ? ad.ad_images!.length - 1 : prev - 1
      )
    }
  }

  const handleNextImage = () => {
    if (ad?.ad_images && ad.ad_images.length > 0) {
      setCurrentImageIndex((prev) =>
        prev === ad.ad_images!.length - 1 ? 0 : prev + 1
      )
    }
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: ad?.title,
          text: ad?.description,
          url: window.location.href,
        })
      } catch (error) {
        console.log('Error sharing:', error)
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      alert('Link copied to clipboard!')
    }
  }

  const toggleFavorite = () => {
    setIsFavorite(!isFavorite)
    // TODO: Implement favorite functionality
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-300 rounded w-1/4 mb-6"></div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="h-96 bg-gray-300 rounded-xl"></div>
              <div className="space-y-4">
                <div className="h-8 bg-gray-300 rounded w-3/4"></div>
                <div className="h-4 bg-gray-300 rounded w-1/2"></div>
                <div className="h-20 bg-gray-300 rounded"></div>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  if (error || !ad) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {error || 'Ad not found'}
            </h1>
            <button
              onClick={() => router.back()}
              className="text-primary-blue hover:text-primary-blue/80"
            >
              Go back
            </button>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6 pt-24">
        {/* Breadcrumb Navigation */}
        <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-4">
          <a href="/" className="hover:text-primary-blue transition-colors">Home</a>
          <span>›</span>
          <a href="/ads" className="hover:text-primary-blue transition-colors">All Ads</a>
          <span>›</span>
          <a href={`/category/${ad.category?.slug}`} className="hover:text-primary-blue transition-colors">
            {ad.category?.name}
          </a>
          <span>›</span>
          <span className="text-gray-900 truncate">{ad.title}</span>
        </nav>

        {/* Title and Actions */}
        <div className="flex items-start justify-between mb-6">
          <h1 className="text-2xl font-semibold font-heading text-gray-900 flex-1 mr-4">{ad.title}</h1>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleShare}
              className="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-primary-blue transition-colors"
            >
              <Share2 className="h-4 w-4 mr-1" />
              Share
            </button>
            <button
              onClick={toggleFavorite}
              className={`flex items-center px-3 py-2 text-sm transition-colors ${
                isFavorite ? 'text-red-600' : 'text-gray-600 hover:text-red-600'
              }`}
            >
              <Heart className={`h-4 w-4 mr-1 ${isFavorite ? 'fill-current' : ''}`} />
              Save ad
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Image Gallery */}
          <div className="lg:col-span-2">
            {/* Main Image */}
            <div className="bg-white border border-gray-200 rounded-lg overflow-hidden mb-4">
              {ad.ad_images && ad.ad_images.length > 0 ? (
                <div className="relative">
                  <div className="relative h-[400px] bg-gray-100">
                    <img
                      src={ad.ad_images[currentImageIndex]?.image_url}
                      alt={ad.title}
                      className="w-full h-full object-cover"
                    />

                    {/* Image Counter */}
                    {ad.ad_images.length > 1 && (
                      <div className="absolute top-3 right-3 bg-black/60 text-white px-2 py-1 rounded text-sm">
                        {currentImageIndex + 1} / {ad.ad_images.length}
                      </div>
                    )}

                    {ad.ad_images.length > 1 && (
                      <>
                        <button
                          onClick={handlePrevImage}
                          className="absolute left-3 top-1/2 transform -translate-y-1/2 bg-white/80 text-gray-800 p-2 rounded-full hover:bg-white shadow-md transition-all duration-200"
                        >
                          <ChevronLeft className="h-4 w-4" />
                        </button>
                        <button
                          onClick={handleNextImage}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 bg-white/80 text-gray-800 p-2 rounded-full hover:bg-white shadow-md transition-all duration-200"
                        >
                          <ChevronRight className="h-4 w-4" />
                        </button>
                      </>
                    )}
                  </div>
                </div>
              ) : (
                <div className="h-[400px] bg-gray-100 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center mx-auto mb-3">
                      <User className="h-8 w-8 text-gray-500" />
                    </div>
                    <p className="text-gray-500">No images available</p>
                  </div>
                </div>
              )}
            </div>

            {/* Thumbnail Gallery */}
            {ad.ad_images && ad.ad_images.length > 1 && (
              <div className="flex space-x-2 overflow-x-auto pb-2">
                {ad.ad_images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-200 ${
                      index === currentImageIndex
                        ? 'border-primary-blue'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <img
                      src={image.image_url}
                      alt={`${ad.title} ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            )}

            {/* Price */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 mt-4">
              <div className="text-3xl font-bold font-heading text-green-600 mb-2">
                {formatCurrency(ad.price)}
                {ad.negotiable && (
                  <span className="text-base font-normal text-gray-500 ml-2">(Negotiable)</span>
                )}
              </div>
              <div className="flex items-center text-sm text-gray-500 space-x-4">
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-1" />
                  {ad.location}
                </div>
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-1" />
                  {formatDate(ad.created_at)}
                </div>
                <div className="flex items-center">
                  <Eye className="h-4 w-4 mr-1" />
                  {ad.views || 0} views
                </div>
              </div>
            </div>

            {/* Product Details */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 mt-4">
              <h3 className="text-lg font-semibold font-heading text-gray-900 mb-4">Details</h3>
              <div className="space-y-3">
                <div className="flex justify-between py-2 border-b border-gray-100">
                  <span className="text-gray-600">Condition:</span>
                  <span className="font-medium text-gray-900 capitalize">{ad.condition}</span>
                </div>
                <div className="flex justify-between py-2 border-b border-gray-100">
                  <span className="text-gray-600">Category:</span>
                  <span className="font-medium text-gray-900">{ad.category?.name}</span>
                </div>
                {ad.subcategory && (
                  <div className="flex justify-between py-2 border-b border-gray-100">
                    <span className="text-gray-600">Subcategory:</span>
                    <span className="font-medium text-gray-900">{ad.subcategory.name}</span>
                  </div>
                )}

              </div>
            </div>

            {/* Description */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 mt-4">
              <h3 className="text-lg font-semibold font-heading text-gray-900 mb-3">Description</h3>
              <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">{ad.description}</p>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-4">
            {/* Seller Info */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                  <User className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-semibold font-heading text-gray-900">
                    {ad.user?.full_name || 'Anonymous'}
                    {isOwnAd && (
                      <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Your Ad
                      </span>
                    )}
                  </h3>
                  <p className="text-sm text-gray-500">Member since March 2023</p>
                </div>
              </div>

              {ad.contact_phone && (
                <div className="mb-4">
                  <div className="flex items-center text-gray-700 mb-2">
                    <Phone className="h-4 w-4 mr-2" />
                    <span className="font-medium">{ad.contact_phone}</span>
                  </div>
                </div>
              )}

              {isOwnAd ? (
                <div className="space-y-2">
                  <div className="w-full flex items-center justify-center px-4 py-3 bg-gray-100 text-gray-600 rounded-lg border-2 border-dashed border-gray-300">
                    <User className="h-4 w-4 mr-2" />
                    This is your ad
                  </div>
                  <p className="text-sm text-gray-500 text-center">
                    You cannot contact yourself. Share this ad with others to get inquiries.
                  </p>
                </div>
              ) : (
                <div className="space-y-2">
                  {ad.contact_phone && (
                    <a
                      href={`tel:${ad.contact_phone}`}
                      className="w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                    >
                      <Phone className="h-4 w-4 mr-2" />
                      Call
                    </a>
                  )}
                  <button
                    onClick={() => {
                      if (!user) {
                        // Redirect to sign in page if user is not authenticated
                        router.push('/auth/signin')
                        return
                      }
                      setIsChatModalOpen(true)
                    }}
                    className={`w-full flex items-center justify-center px-4 py-2 rounded-lg transition-colors ${
                      !user
                        ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                        : 'bg-blue-600 text-white hover:bg-blue-700'
                    }`}
                    title={!user ? 'Please sign in to chat' : 'Chat with seller'}
                  >
                    <MessageCircle className="h-4 w-4 mr-2" />
                    {!user ? 'Sign in to Chat' : 'Chat'}
                  </button>
                </div>
              )}
            </div>

            {/* Safety Alert */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <Shield className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
                <div className="ml-3">
                  <h4 className="text-sm font-semibold text-blue-900 mb-1">Stay Alert: Avoid Online Scams</h4>
                  <ul className="text-xs text-blue-800 space-y-1">
                    <li>• Never share card details or OTPs, and avoid making payments through links sent to you.</li>
                    <li>• Verify seller forms in person before making any payment.</li>
                    <li>• OKDOI does not offer a delivery service. Buy vigilant!</li>
                  </ul>
                  <button className="text-xs text-blue-600 hover:text-blue-800 mt-2">
                    See all safety tips
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* More ads from this member */}
        <div className="mt-12">
          <AdSlider
            ads={memberAds}
            title="More ads from this member"
            loading={relatedLoading}
          />
        </div>

        {/* Similar ads */}
        <div className="mt-12">
          <AdSlider
            ads={similarAds}
            title="Similar ads"
            loading={relatedLoading}
          />
        </div>
      </div>

      <Footer />

      {/* Chat Modal - Only show if not own ad */}
      {!isOwnAd && (
        <ChatModal
          isOpen={isChatModalOpen}
          onClose={() => setIsChatModalOpen(false)}
          ad={ad}
        />
      )}
    </div>
  )
}
