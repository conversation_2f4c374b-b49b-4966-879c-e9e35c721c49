-- Update deposit_requests table to support new deposit request structure
-- This migration modifies the table to match the new requirements

-- First, add the new depositor_name column
ALTER TABLE deposit_requests 
ADD COLUMN depositor_name varchar(100);

-- Make bank_name, account_holder_name, account_number optional (nullable)
-- since the new structure doesn't require these fields
ALTER TABLE deposit_requests 
ALTER COLUMN bank_name DROP NOT NULL,
ALTER COLUMN account_holder_name DROP NOT NULL,
ALTER COLUMN account_number DROP NOT NULL;

-- Update existing records to have depositor_name based on account_holder_name
UPDATE deposit_requests 
SET depositor_name = account_holder_name 
WHERE depositor_name IS NULL AND account_holder_name IS NOT NULL;

-- For any remaining NULL depositor_name records, set a default
UPDATE deposit_requests 
SET depositor_name = 'Unknown Depositor' 
WHERE depositor_name IS NULL;

-- Now make depositor_name NOT NULL since it's required in the new structure
ALTER TABLE deposit_requests 
ALTER COLUMN depositor_name SET NOT NULL;

-- Add comment to document the schema change
COMMENT ON COLUMN deposit_requests.depositor_name IS 'Name of the person who made the deposit (required)';
COMMENT ON COLUMN deposit_requests.bank_name IS 'Bank name (optional in new structure)';
COMMENT ON COLUMN deposit_requests.account_holder_name IS 'Account holder name (optional in new structure)';
COMMENT ON COLUMN deposit_requests.account_number IS 'Account number (optional in new structure)';
