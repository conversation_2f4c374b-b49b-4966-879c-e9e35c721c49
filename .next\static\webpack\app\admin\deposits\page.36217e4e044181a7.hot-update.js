"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/deposits/page",{

/***/ "(app-pages-browser)/./src/app/admin/deposits/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/admin/deposits/page.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminDepositsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Banknote,Calendar,CheckCircle,Clock,Eye,MoreVertical,Search,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/admin/AdminLayout */ \"(app-pages-browser)/./src/components/admin/AdminLayout.tsx\");\n/* harmony import */ var _lib_services_wallet__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/wallet */ \"(app-pages-browser)/./src/lib/services/wallet.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nfunction DepositActions(param) {\n    let { deposit, onApprove, onReject } = param;\n    _s();\n    const [showMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setShowMenu(!showMenu),\n                className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-1\",\n                    children: deposit.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    onApprove(deposit.id);\n                                    setShowMenu(false);\n                                },\n                                className: \"flex items-center w-full px-4 py-2 text-sm text-green-600 hover:bg-green-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Approve Deposit\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    onReject(deposit.id);\n                                    setShowMenu(false);\n                                },\n                                className: \"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Reject Deposit\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n_s(DepositActions, \"2FjIcsdimgVhm2IsUWodA2ftTZU=\");\n_c = DepositActions;\nfunction AdminDepositsPage() {\n    var _selectedDeposit_user;\n    _s1();\n    const [deposits, setDeposits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalDeposits, setTotalDeposits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const depositsPerPage = 20;\n    // Modal states\n    const [showRejectModal, setShowRejectModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showModifyModal, setShowModifyModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedDeposit, setSelectedDeposit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rejectNotes, setRejectNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [modifiedAmount, setModifiedAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchDeposits();\n    }, [\n        currentPage,\n        selectedStatus\n    ]);\n    const fetchDeposits = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const status = selectedStatus === \"all\" ? undefined : selectedStatus;\n            const { requests, total } = await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_3__.WalletService.getAllDepositRequests(currentPage, depositsPerPage, status);\n            setDeposits(requests || []);\n            setTotalDeposits(total || 0);\n        } catch (err) {\n            console.error(\"Error fetching deposits:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to load deposits\");\n            setDeposits([]);\n            setTotalDeposits(0);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleApprove = async (depositId)=>{\n        if (!confirm(\"Are you sure you want to approve this deposit?\")) return;\n        try {\n            setActionLoading(true);\n            const { data: { user } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_5__.supabase.auth.getUser();\n            if (!user) throw new Error(\"Not authenticated\");\n            await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_3__.WalletService.approveDepositRequest(depositId, user.id);\n            await fetchDeposits();\n            alert(\"Deposit approved successfully!\");\n        } catch (err) {\n            alert(err instanceof Error ? err.message : \"Failed to approve deposit\");\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleReject = (depositId)=>{\n        const deposit = deposits.find((d)=>d.id === depositId);\n        if (!deposit) return;\n        setSelectedDeposit(deposit);\n        setShowRejectModal(true);\n    };\n    const confirmReject = async ()=>{\n        if (!selectedDeposit || !rejectNotes.trim()) return;\n        try {\n            setActionLoading(true);\n            const { data: { user } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_5__.supabase.auth.getUser();\n            if (!user) throw new Error(\"Not authenticated\");\n            await _lib_services_wallet__WEBPACK_IMPORTED_MODULE_3__.WalletService.rejectDepositRequest(selectedDeposit.id, user.id, rejectNotes);\n            setShowRejectModal(false);\n            setSelectedDeposit(null);\n            setRejectNotes(\"\");\n            await fetchDeposits();\n            alert(\"Deposit rejected successfully!\");\n        } catch (err) {\n            alert(err instanceof Error ? err.message : \"Failed to reject deposit\");\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"approved\":\n                return \"text-green-600 bg-green-100\";\n            case \"pending\":\n                return \"text-yellow-600 bg-yellow-100\";\n            case \"rejected\":\n                return \"text-red-600 bg-red-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"approved\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 16\n                }, this);\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 16\n                }, this);\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const filteredDeposits = deposits.filter((deposit)=>{\n        var _deposit_user_full_name, _deposit_user, _deposit_user_email, _deposit_user1;\n        const matchesSearch = ((_deposit_user = deposit.user) === null || _deposit_user === void 0 ? void 0 : (_deposit_user_full_name = _deposit_user.full_name) === null || _deposit_user_full_name === void 0 ? void 0 : _deposit_user_full_name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_deposit_user1 = deposit.user) === null || _deposit_user1 === void 0 ? void 0 : (_deposit_user_email = _deposit_user1.email) === null || _deposit_user_email === void 0 ? void 0 : _deposit_user_email.toLowerCase().includes(searchTerm.toLowerCase())) || deposit.bank_name.toLowerCase().includes(searchTerm.toLowerCase()) || deposit.account_holder_name.toLowerCase().includes(searchTerm.toLowerCase());\n        return matchesSearch;\n    });\n    const totalPages = Math.ceil(totalDeposits / depositsPerPage);\n    const statusCounts = {\n        all: totalDeposits,\n        pending: deposits.filter((d)=>d.status === \"pending\").length,\n        approved: deposits.filter((d)=>d.status === \"approved\").length,\n        rejected: deposits.filter((d)=>d.status === \"rejected\").length\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Deposit Requests\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Review and manage user deposit requests\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            totalDeposits,\n                                            \" total requests\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                        children: Object.entries(statusCounts).map((param)=>{\n                            let [status, count] = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>setSelectedStatus(status),\n                                className: \"p-4 rounded-lg border cursor-pointer transition-colors \".concat(selectedStatus === status ? \"border-primary-blue bg-primary-blue/5\" : \"border-gray-200 hover:border-gray-300\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: count\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600 capitalize\",\n                                        children: status === \"all\" ? \"Total Requests\" : \"\".concat(status, \" Requests\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, status, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search by user, bank, or account holder...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 13\n                        }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: fetchDeposits,\n                                    className: \"px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90\",\n                                    children: \"Try Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full divide-y divide-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"User\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Amount\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Depositor & Reference\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Submitted\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-white divide-y divide-gray-200\",\n                                        children: filteredDeposits.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                colSpan: 6,\n                                                className: \"px-6 py-12 text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-12 w-12 text-gray-400 mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                            children: \"No deposit requests found\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500\",\n                                                            children: searchTerm ? \"Try adjusting your search terms.\" : \"No deposit requests have been submitted yet.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 21\n                                        }, this) : filteredDeposits.map((deposit)=>{\n                                            var _deposit_user, _deposit_user1;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-10 w-10 rounded-full bg-primary-blue/10 flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-primary-blue\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                            children: ((_deposit_user = deposit.user) === null || _deposit_user === void 0 ? void 0 : _deposit_user.full_name) || \"No name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                            lineNumber: 323,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: ((_deposit_user1 = deposit.user) === null || _deposit_user1 === void 0 ? void 0 : _deposit_user1.email) || \"No email\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                            lineNumber: 326,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-bold text-gray-900\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(deposit.amount)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-900\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        \"Depositor: \",\n                                                                        deposit.depositor_name || \"Not specified\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                deposit.reference_number && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-400 font-mono\",\n                                                                    children: [\n                                                                        \"Ref: \",\n                                                                        deposit.reference_number\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                deposit.deposit_slip_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                        href: deposit.deposit_slip_url,\n                                                                        target: \"_blank\",\n                                                                        rel: \"noopener noreferrer\",\n                                                                        className: \"inline-flex items-center text-xs text-blue-600 hover:text-blue-800\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"h-3 w-3 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                                lineNumber: 353,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            \"View Proof\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                        lineNumber: 347,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(getStatusColor(deposit.status)),\n                                                            children: [\n                                                                getStatusIcon(deposit.status),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-1 capitalize\",\n                                                                    children: deposit.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                    lineNumber: 363,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 text-sm text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Banknote_Calendar_CheckCircle_Clock_Eye_MoreVertical_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                        lineNumber: 368,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    new Date(deposit.created_at).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: new Date(deposit.created_at).toLocaleTimeString()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DepositActions, {\n                                                            deposit: deposit,\n                                                            onApprove: handleApprove,\n                                                            onReject: handleReject\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, deposit.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, this),\n                    totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-700\",\n                                children: [\n                                    \"Showing \",\n                                    (currentPage - 1) * depositsPerPage + 1,\n                                    \" to \",\n                                    Math.min(currentPage * depositsPerPage, totalDeposits),\n                                    \" of \",\n                                    totalDeposits,\n                                    \" requests\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setCurrentPage((prev)=>Math.max(prev - 1, 1)),\n                                        disabled: currentPage === 1,\n                                        className: \"px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\",\n                                        children: \"Previous\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 15\n                                    }, this),\n                                    Array.from({\n                                        length: Math.min(5, totalPages)\n                                    }, (_, i)=>{\n                                        const page = i + Math.max(1, currentPage - 2);\n                                        return page <= totalPages ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentPage(page),\n                                            className: \"px-3 py-2 border rounded-lg \".concat(currentPage === page ? \"bg-primary-blue text-white border-primary-blue\" : \"border-gray-300 hover:bg-gray-50\"),\n                                            children: page\n                                        }, page, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 19\n                                        }, this) : null;\n                                    }),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setCurrentPage((prev)=>Math.min(prev + 1, totalPages)),\n                                        disabled: currentPage === totalPages,\n                                        className: \"px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\",\n                                        children: \"Next\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, this),\n            showRejectModal && selectedDeposit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Reject Deposit Request\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 439,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mb-2\",\n                                children: [\n                                    \"You are about to reject a deposit request for\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(selectedDeposit.amount)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" \",\n                                    \"from \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: (_selectedDeposit_user = selectedDeposit.user) === null || _selectedDeposit_user === void 0 ? void 0 : _selectedDeposit_user.full_name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 22\n                                    }, this),\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Reason for rejection *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    required: true,\n                                    value: rejectNotes,\n                                    onChange: (e)=>setRejectNotes(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                    rows: 4,\n                                    placeholder: \"Please provide a reason for rejecting this deposit request...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 447,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>{\n                                        setShowRejectModal(false);\n                                        setSelectedDeposit(null);\n                                        setRejectNotes(\"\");\n                                    },\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                    lineNumber: 461,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: confirmReject,\n                                    disabled: actionLoading || !rejectNotes.trim(),\n                                    className: \"flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50\",\n                                    children: actionLoading ? \"Rejecting...\" : \"Reject Deposit\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                            lineNumber: 460,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                    lineNumber: 438,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n                lineNumber: 437,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\deposits\\\\page.tsx\",\n        lineNumber: 210,\n        columnNumber: 5\n    }, this);\n}\n_s1(AdminDepositsPage, \"IT94xPlRLdb2MkMAHMGQYy02XII=\");\n_c1 = AdminDepositsPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"DepositActions\");\n$RefreshReg$(_c1, \"AdminDepositsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYWRtaW4vZGVwb3NpdHMvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUUyQztBQWF0QjtBQUNtQztBQUNIO0FBRVQ7QUFDSDtBQVF6QyxTQUFTZSxlQUFlLEtBQXFEO1FBQXJELEVBQUVDLE9BQU8sRUFBRUMsU0FBUyxFQUFFQyxRQUFRLEVBQXVCLEdBQXJEOztJQUN0QixNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR3BCLCtDQUFRQSxDQUFDO0lBRXpDLHFCQUNFLDhEQUFDcUI7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNDO2dCQUNDQyxTQUFTLElBQU1KLFlBQVksQ0FBQ0Q7Z0JBQzVCRyxXQUFVOzBCQUVWLDRFQUFDbEIsb0pBQVlBO29CQUFDa0IsV0FBVTs7Ozs7Ozs7Ozs7WUFHekJILDBCQUNDLDhEQUFDRTtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ1pOLFFBQVFTLE1BQU0sS0FBSywyQkFDbEI7OzBDQUNFLDhEQUFDRjtnQ0FDQ0MsU0FBUztvQ0FDUFAsVUFBVUQsUUFBUVUsRUFBRTtvQ0FDcEJOLFlBQVk7Z0NBQ2Q7Z0NBQ0FFLFdBQVU7O2tEQUVWLDhEQUFDakIsb0pBQVdBO3dDQUFDaUIsV0FBVTs7Ozs7O29DQUFpQjs7Ozs7OzswQ0FHMUMsOERBQUNDO2dDQUNDQyxTQUFTO29DQUNQTixTQUFTRixRQUFRVSxFQUFFO29DQUNuQk4sWUFBWTtnQ0FDZDtnQ0FDQUUsV0FBVTs7a0RBRVYsOERBQUNoQixvSkFBT0E7d0NBQUNnQixXQUFVOzs7Ozs7b0NBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVXREO0dBNUNTUDtLQUFBQTtBQThDTSxTQUFTWTtRQWlYNEJDOztJQWhYbEQsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUc5QiwrQ0FBUUEsQ0FBbUIsRUFBRTtJQUM3RCxNQUFNLENBQUMrQixTQUFTQyxXQUFXLEdBQUdoQywrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNpQyxPQUFPQyxTQUFTLEdBQUdsQywrQ0FBUUEsQ0FBZ0I7SUFDbEQsTUFBTSxDQUFDbUMsWUFBWUMsY0FBYyxHQUFHcEMsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDcUMsZ0JBQWdCQyxrQkFBa0IsR0FBR3RDLCtDQUFRQSxDQUFTO0lBQzdELE1BQU0sQ0FBQ3VDLGFBQWFDLGVBQWUsR0FBR3hDLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ3lDLGVBQWVDLGlCQUFpQixHQUFHMUMsK0NBQVFBLENBQUM7SUFDbkQsTUFBTTJDLGtCQUFrQjtJQUV4QixlQUFlO0lBQ2YsTUFBTSxDQUFDQyxpQkFBaUJDLG1CQUFtQixHQUFHN0MsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDOEMsaUJBQWlCQyxtQkFBbUIsR0FBRy9DLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQzRCLGlCQUFpQm9CLG1CQUFtQixHQUFHaEQsK0NBQVFBLENBQXdCO0lBQzlFLE1BQU0sQ0FBQ2lELGFBQWFDLGVBQWUsR0FBR2xELCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ21ELGdCQUFnQkMsa0JBQWtCLEdBQUdwRCwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUNxRCxlQUFlQyxpQkFBaUIsR0FBR3RELCtDQUFRQSxDQUFDO0lBRW5EQyxnREFBU0EsQ0FBQztRQUNSc0Q7SUFDRixHQUFHO1FBQUNoQjtRQUFhRjtLQUFlO0lBRWhDLE1BQU1rQixnQkFBZ0I7UUFDcEIsSUFBSTtZQUNGdkIsV0FBVztZQUNYRSxTQUFTO1lBQ1QsTUFBTVQsU0FBU1ksbUJBQW1CLFFBQVFtQixZQUFZbkI7WUFDdEQsTUFBTSxFQUFFb0IsUUFBUSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNOUMsK0RBQWFBLENBQUMrQyxxQkFBcUIsQ0FDbkVwQixhQUNBSSxpQkFDQWxCO1lBRUZLLFlBQVkyQixZQUFZLEVBQUU7WUFDMUJmLGlCQUFpQmdCLFNBQVM7UUFDNUIsRUFBRSxPQUFPRSxLQUFLO1lBQ1pDLFFBQVE1QixLQUFLLENBQUMsNEJBQTRCMkI7WUFDMUMxQixTQUFTMEIsZUFBZUUsUUFBUUYsSUFBSUcsT0FBTyxHQUFHO1lBQzlDakMsWUFBWSxFQUFFO1lBQ2RZLGlCQUFpQjtRQUNuQixTQUFVO1lBQ1JWLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTWdDLGdCQUFnQixPQUFPQztRQUMzQixJQUFJLENBQUNDLFFBQVEsbURBQW1EO1FBRWhFLElBQUk7WUFDRlosaUJBQWlCO1lBQ2pCLE1BQU0sRUFBRWEsTUFBTSxFQUFFQyxJQUFJLEVBQUUsRUFBRSxHQUFHLE1BQU10RCxtREFBUUEsQ0FBQ3VELElBQUksQ0FBQ0MsT0FBTztZQUN0RCxJQUFJLENBQUNGLE1BQU0sTUFBTSxJQUFJTixNQUFNO1lBRTNCLE1BQU1sRCwrREFBYUEsQ0FBQzJELHFCQUFxQixDQUFDTixXQUFXRyxLQUFLMUMsRUFBRTtZQUM1RCxNQUFNNkI7WUFDTmlCLE1BQU07UUFDUixFQUFFLE9BQU9aLEtBQUs7WUFDWlksTUFBTVosZUFBZUUsUUFBUUYsSUFBSUcsT0FBTyxHQUFHO1FBQzdDLFNBQVU7WUFDUlQsaUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQSxNQUFNbUIsZUFBZSxDQUFDUjtRQUNwQixNQUFNakQsVUFBVWEsU0FBUzZDLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRWpELEVBQUUsS0FBS3VDO1FBQzVDLElBQUksQ0FBQ2pELFNBQVM7UUFFZGdDLG1CQUFtQmhDO1FBQ25CNkIsbUJBQW1CO0lBQ3JCO0lBRUEsTUFBTStCLGdCQUFnQjtRQUNwQixJQUFJLENBQUNoRCxtQkFBbUIsQ0FBQ3FCLFlBQVk0QixJQUFJLElBQUk7UUFFN0MsSUFBSTtZQUNGdkIsaUJBQWlCO1lBQ2pCLE1BQU0sRUFBRWEsTUFBTSxFQUFFQyxJQUFJLEVBQUUsRUFBRSxHQUFHLE1BQU10RCxtREFBUUEsQ0FBQ3VELElBQUksQ0FBQ0MsT0FBTztZQUN0RCxJQUFJLENBQUNGLE1BQU0sTUFBTSxJQUFJTixNQUFNO1lBRTNCLE1BQU1sRCwrREFBYUEsQ0FBQ2tFLG9CQUFvQixDQUFDbEQsZ0JBQWdCRixFQUFFLEVBQUUwQyxLQUFLMUMsRUFBRSxFQUFFdUI7WUFDdEVKLG1CQUFtQjtZQUNuQkcsbUJBQW1CO1lBQ25CRSxlQUFlO1lBQ2YsTUFBTUs7WUFDTmlCLE1BQU07UUFDUixFQUFFLE9BQU9aLEtBQUs7WUFDWlksTUFBTVosZUFBZUUsUUFBUUYsSUFBSUcsT0FBTyxHQUFHO1FBQzdDLFNBQVU7WUFDUlQsaUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQSxNQUFNeUIsaUJBQWlCLENBQUN0RDtRQUN0QixPQUFRQTtZQUNOLEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNUO2dCQUNFLE9BQU87UUFDWDtJQUNGO0lBRUEsTUFBTXVELGdCQUFnQixDQUFDdkQ7UUFDckIsT0FBUUE7WUFDTixLQUFLO2dCQUNILHFCQUFPLDhEQUFDcEIsb0pBQVdBO29CQUFDaUIsV0FBVTs7Ozs7O1lBQ2hDLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNmLG9KQUFLQTtvQkFBQ2UsV0FBVTs7Ozs7O1lBQzFCLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNoQixvSkFBT0E7b0JBQUNnQixXQUFVOzs7Ozs7WUFDNUI7Z0JBQ0UscUJBQU8sOERBQUNmLG9KQUFLQTtvQkFBQ2UsV0FBVTs7Ozs7O1FBQzVCO0lBQ0Y7SUFFQSxNQUFNMkQsbUJBQW1CcEQsU0FBU3FELE1BQU0sQ0FBQ2xFLENBQUFBO1lBQ2pCQSx5QkFBQUEsZUFDREEscUJBQUFBO1FBRHJCLE1BQU1tRSxnQkFBZ0JuRSxFQUFBQSxnQkFBQUEsUUFBUW9ELElBQUksY0FBWnBELHFDQUFBQSwwQkFBQUEsY0FBY29FLFNBQVMsY0FBdkJwRSw4Q0FBQUEsd0JBQXlCcUUsV0FBVyxHQUFHQyxRQUFRLENBQUNuRCxXQUFXa0QsV0FBVyxVQUN2RXJFLGlCQUFBQSxRQUFRb0QsSUFBSSxjQUFacEQsc0NBQUFBLHNCQUFBQSxlQUFjdUUsS0FBSyxjQUFuQnZFLDBDQUFBQSxvQkFBcUJxRSxXQUFXLEdBQUdDLFFBQVEsQ0FBQ25ELFdBQVdrRCxXQUFXLFFBQ2xFckUsUUFBUXdFLFNBQVMsQ0FBQ0gsV0FBVyxHQUFHQyxRQUFRLENBQUNuRCxXQUFXa0QsV0FBVyxPQUMvRHJFLFFBQVF5RSxtQkFBbUIsQ0FBQ0osV0FBVyxHQUFHQyxRQUFRLENBQUNuRCxXQUFXa0QsV0FBVztRQUM5RixPQUFPRjtJQUNUO0lBRUEsTUFBTU8sYUFBYUMsS0FBS0MsSUFBSSxDQUFDbkQsZ0JBQWdCRTtJQUU3QyxNQUFNa0QsZUFBZTtRQUNuQkMsS0FBS3JEO1FBQ0xzRCxTQUFTbEUsU0FBU3FELE1BQU0sQ0FBQ1AsQ0FBQUEsSUFBS0EsRUFBRWxELE1BQU0sS0FBSyxXQUFXdUUsTUFBTTtRQUM1REMsVUFBVXBFLFNBQVNxRCxNQUFNLENBQUNQLENBQUFBLElBQUtBLEVBQUVsRCxNQUFNLEtBQUssWUFBWXVFLE1BQU07UUFDOURFLFVBQVVyRSxTQUFTcUQsTUFBTSxDQUFDUCxDQUFBQSxJQUFLQSxFQUFFbEQsTUFBTSxLQUFLLFlBQVl1RSxNQUFNO0lBQ2hFO0lBRUEscUJBQ0UsOERBQUNyRixxRUFBV0E7OzBCQUNWLDhEQUFDVTtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7O2tEQUNDLDhEQUFDOEU7d0NBQUc3RSxXQUFVO2tEQUFtQzs7Ozs7O2tEQUNqRCw4REFBQzhFO3dDQUFFOUUsV0FBVTtrREFBZ0I7Ozs7Ozs7Ozs7OzswQ0FFL0IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3BCLHFKQUFRQTt3Q0FBQ29CLFdBQVU7Ozs7OztrREFDcEIsOERBQUMrRTs7NENBQU01RDs0Q0FBYzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLekIsOERBQUNwQjt3QkFBSUMsV0FBVTtrQ0FDWmdGLE9BQU9DLE9BQU8sQ0FBQ1YsY0FBY1csR0FBRyxDQUFDO2dDQUFDLENBQUMvRSxRQUFRZ0YsTUFBTTtpREFDaEQsOERBQUNwRjtnQ0FFQ0csU0FBUyxJQUFNYyxrQkFBa0JiO2dDQUNqQ0gsV0FBVywwREFJVixPQUhDZSxtQkFBbUJaLFNBQ2YsMENBQ0E7O2tEQUdOLDhEQUFDSjt3Q0FBSUMsV0FBVTtrREFBb0NtRjs7Ozs7O2tEQUNuRCw4REFBQ3BGO3dDQUFJQyxXQUFVO2tEQUNaRyxXQUFXLFFBQVEsbUJBQW1CLEdBQVUsT0FBUEEsUUFBTzs7Ozs7OzsrQkFWOUNBOzs7Ozs7Ozs7OztrQ0FpQlgsOERBQUNKO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDbkIscUpBQU1BO3dDQUFDbUIsV0FBVTs7Ozs7O2tEQUNsQiw4REFBQ29GO3dDQUNDQyxNQUFLO3dDQUNMQyxhQUFZO3dDQUNaQyxPQUFPMUU7d0NBQ1AyRSxVQUFVLENBQUNDLElBQU0zRSxjQUFjMkUsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO3dDQUM3Q3ZGLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBT2xCLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDWlMsd0JBQ0MsOERBQUNWOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRDtnQ0FBSUMsV0FBVTs7Ozs7Ozs7OzttQ0FFZlcsc0JBQ0YsOERBQUNaOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQXFCVzs7Ozs7OzhDQUNwQyw4REFBQ1Y7b0NBQ0NDLFNBQVMrQjtvQ0FDVGpDLFdBQVU7OENBQ1g7Ozs7Ozs7Ozs7O2lEQUtILDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQzJGO2dDQUFNM0YsV0FBVTs7a0RBQ2YsOERBQUM0Rjt3Q0FBTTVGLFdBQVU7a0RBQ2YsNEVBQUM2Rjs7OERBQ0MsOERBQUNDO29EQUFHOUYsV0FBVTs4REFBaUY7Ozs7Ozs4REFHL0YsOERBQUM4RjtvREFBRzlGLFdBQVU7OERBQWlGOzs7Ozs7OERBRy9GLDhEQUFDOEY7b0RBQUc5RixXQUFVOzhEQUFpRjs7Ozs7OzhEQUcvRiw4REFBQzhGO29EQUFHOUYsV0FBVTs4REFBaUY7Ozs7Ozs4REFHL0YsOERBQUM4RjtvREFBRzlGLFdBQVU7OERBQWlGOzs7Ozs7OERBRy9GLDhEQUFDOEY7b0RBQUc5RixXQUFVOzhEQUFrRjs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBS3BHLDhEQUFDK0Y7d0NBQU0vRixXQUFVO2tEQUNkMkQsaUJBQWlCZSxNQUFNLEtBQUssa0JBQzNCLDhEQUFDbUI7c0RBQ0MsNEVBQUNHO2dEQUFHQyxTQUFTO2dEQUFHakcsV0FBVTswREFDeEIsNEVBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ3BCLHFKQUFRQTs0REFBQ29CLFdBQVU7Ozs7OztzRUFDcEIsOERBQUNrRzs0REFBR2xHLFdBQVU7c0VBQXlDOzs7Ozs7c0VBQ3ZELDhEQUFDOEU7NERBQUU5RSxXQUFVO3NFQUNWYSxhQUFhLHFDQUFxQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O21EQU0zRDhDLGlCQUFpQnVCLEdBQUcsQ0FBQyxDQUFDeEY7Z0RBU1RBLGVBRXFDQTtpRUFWaEQsOERBQUNtRztnREFBb0I3RixXQUFVOztrRUFDN0IsOERBQUNnRzt3REFBR2hHLFdBQVU7a0VBQ1osNEVBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQUlDLFdBQVU7OEVBQ2IsNEVBQUNiLHFKQUFJQTt3RUFBQ2EsV0FBVTs7Ozs7Ozs7Ozs7OEVBRWxCLDhEQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNEOzRFQUFJQyxXQUFVO3NGQUNaTixFQUFBQSxnQkFBQUEsUUFBUW9ELElBQUksY0FBWnBELG9DQUFBQSxjQUFjb0UsU0FBUyxLQUFJOzs7Ozs7c0ZBRTlCLDhEQUFDL0Q7NEVBQUlDLFdBQVU7c0ZBQXlCTixFQUFBQSxpQkFBQUEsUUFBUW9ELElBQUksY0FBWnBELHFDQUFBQSxlQUFjdUUsS0FBSyxLQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFJdkUsOERBQUMrQjt3REFBR2hHLFdBQVU7a0VBQ1osNEVBQUNEOzREQUFJQyxXQUFVO3NFQUNaVCwwREFBY0EsQ0FBQ0csUUFBUXlHLE1BQU07Ozs7Ozs7Ozs7O2tFQUdsQyw4REFBQ0g7d0RBQUdoRyxXQUFVO2tFQUNaLDRFQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEO29FQUFJQyxXQUFVOzt3RUFBYzt3RUFDZk4sUUFBUTBHLGNBQWMsSUFBSTs7Ozs7OztnRUFFdkMxRyxRQUFRMkcsZ0JBQWdCLGtCQUN2Qiw4REFBQ3RHO29FQUFJQyxXQUFVOzt3RUFBa0M7d0VBQ3pDTixRQUFRMkcsZ0JBQWdCOzs7Ozs7O2dFQUdqQzNHLFFBQVE0RyxnQkFBZ0Isa0JBQ3ZCLDhEQUFDdkc7b0VBQUlDLFdBQVU7OEVBQ2IsNEVBQUN1Rzt3RUFDQ0MsTUFBTTlHLFFBQVE0RyxnQkFBZ0I7d0VBQzlCWixRQUFPO3dFQUNQZSxLQUFJO3dFQUNKekcsV0FBVTs7MEZBRVYsOERBQUNkLHFKQUFHQTtnRkFBQ2MsV0FBVTs7Ozs7OzRFQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBTzFDLDhEQUFDZ0c7d0RBQUdoRyxXQUFVO2tFQUNaLDRFQUFDK0U7NERBQUsvRSxXQUFXLDJFQUEwRyxPQUEvQnlELGVBQWUvRCxRQUFRUyxNQUFNOztnRUFDdEh1RCxjQUFjaEUsUUFBUVMsTUFBTTs4RUFDN0IsOERBQUM0RTtvRUFBSy9FLFdBQVU7OEVBQW1CTixRQUFRUyxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7OztrRUFHckQsOERBQUM2Rjt3REFBR2hHLFdBQVU7OzBFQUNaLDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNaLHFKQUFRQTt3RUFBQ1ksV0FBVTs7Ozs7O29FQUNuQixJQUFJMEcsS0FBS2hILFFBQVFpSCxVQUFVLEVBQUVDLGtCQUFrQjs7Ozs7OzswRUFFbEQsOERBQUM3RztnRUFBSUMsV0FBVTswRUFDWixJQUFJMEcsS0FBS2hILFFBQVFpSCxVQUFVLEVBQUVFLGtCQUFrQjs7Ozs7Ozs7Ozs7O2tFQUdwRCw4REFBQ2I7d0RBQUdoRyxXQUFVO2tFQUNaLDRFQUFDUDs0REFDQ0MsU0FBU0E7NERBQ1RDLFdBQVcrQzs0REFDWDlDLFVBQVV1RDs7Ozs7Ozs7Ozs7OytDQS9ETHpELFFBQVFVLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQkE0RWhDZ0UsYUFBYSxtQkFDWiw4REFBQ3JFO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O29DQUF3QjtvQ0FDMUJpQixDQUFBQSxjQUFjLEtBQUtJLGtCQUFtQjtvQ0FBRTtvQ0FBS2dELEtBQUt5QyxHQUFHLENBQUM3RixjQUFjSSxpQkFBaUJGO29DQUFlO29DQUFLQTtvQ0FBYzs7Ozs7OzswQ0FFcEksOERBQUNwQjtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNDO3dDQUNDQyxTQUFTLElBQU1nQixlQUFlNkYsQ0FBQUEsT0FBUTFDLEtBQUsyQyxHQUFHLENBQUNELE9BQU8sR0FBRzt3Q0FDekRFLFVBQVVoRyxnQkFBZ0I7d0NBQzFCakIsV0FBVTtrREFDWDs7Ozs7O29DQUlBa0gsTUFBTUMsSUFBSSxDQUFDO3dDQUFFekMsUUFBUUwsS0FBS3lDLEdBQUcsQ0FBQyxHQUFHMUM7b0NBQVksR0FBRyxDQUFDZ0QsR0FBR0M7d0NBQ25ELE1BQU1DLE9BQU9ELElBQUloRCxLQUFLMkMsR0FBRyxDQUFDLEdBQUcvRixjQUFjO3dDQUMzQyxPQUFPcUcsUUFBUWxELDJCQUNiLDhEQUFDbkU7NENBRUNDLFNBQVMsSUFBTWdCLGVBQWVvRzs0Q0FDOUJ0SCxXQUFXLCtCQUlWLE9BSENpQixnQkFBZ0JxRyxPQUNaLG1EQUNBO3NEQUdMQTsyQ0FSSUE7Ozs7bURBVUw7b0NBQ047a0RBRUEsOERBQUNySDt3Q0FDQ0MsU0FBUyxJQUFNZ0IsZUFBZTZGLENBQUFBLE9BQVExQyxLQUFLeUMsR0FBRyxDQUFDQyxPQUFPLEdBQUczQzt3Q0FDekQ2QyxVQUFVaEcsZ0JBQWdCbUQ7d0NBQzFCcEUsV0FBVTtrREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBU1JzQixtQkFBbUJoQixpQ0FDbEIsOERBQUNQO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNrRzs0QkFBR2xHLFdBQVU7c0NBQTJDOzs7Ozs7c0NBQ3pELDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQzhFO2dDQUFFOUUsV0FBVTs7b0NBQTZCO29DQUNNO2tEQUM5Qyw4REFBQytFO3dDQUFLL0UsV0FBVTtrREFBZVQsMERBQWNBLENBQUNlLGdCQUFnQjZGLE1BQU07Ozs7OztvQ0FBVTtvQ0FBSTtrREFDN0UsOERBQUNwQjt3Q0FBSy9FLFdBQVU7bURBQWVNLHdCQUFBQSxnQkFBZ0J3QyxJQUFJLGNBQXBCeEMsNENBQUFBLHNCQUFzQndELFNBQVM7Ozs7OztvQ0FBUTs7Ozs7Ozs7Ozs7O3NDQUcvRSw4REFBQy9EOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ3VIO29DQUFNdkgsV0FBVTs4Q0FBK0M7Ozs7Ozs4Q0FHaEUsOERBQUN3SDtvQ0FDQ0MsUUFBUTtvQ0FDUmxDLE9BQU81RDtvQ0FDUDZELFVBQVUsQ0FBQ0MsSUFBTTdELGVBQWU2RCxFQUFFQyxNQUFNLENBQUNILEtBQUs7b0NBQzlDdkYsV0FBVTtvQ0FDVjBILE1BQU07b0NBQ05wQyxhQUFZOzs7Ozs7Ozs7Ozs7c0NBR2hCLDhEQUFDdkY7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDQztvQ0FDQ29GLE1BQUs7b0NBQ0xuRixTQUFTO3dDQUNQcUIsbUJBQW1CO3dDQUNuQkcsbUJBQW1CO3dDQUNuQkUsZUFBZTtvQ0FDakI7b0NBQ0E1QixXQUFVOzhDQUNYOzs7Ozs7OENBR0QsOERBQUNDO29DQUNDQyxTQUFTb0Q7b0NBQ1QyRCxVQUFVbEYsaUJBQWlCLENBQUNKLFlBQVk0QixJQUFJO29DQUM1Q3ZELFdBQVU7OENBRVQrQixnQkFBZ0IsaUJBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVFsRDtJQTFad0IxQjtNQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL2FkbWluL2RlcG9zaXRzL3BhZ2UudHN4P2RhZmEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IFxuICBCYW5rbm90ZSwgXG4gIFNlYXJjaCwgXG4gIEZpbHRlcixcbiAgTW9yZVZlcnRpY2FsLFxuICBDaGVja0NpcmNsZSxcbiAgWENpcmNsZSxcbiAgQ2xvY2ssXG4gIEV5ZSxcbiAgVXNlcixcbiAgQ2FsZW5kYXIsXG4gIERvbGxhclNpZ25cbn0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuaW1wb3J0IEFkbWluTGF5b3V0IGZyb20gJ0AvY29tcG9uZW50cy9hZG1pbi9BZG1pbkxheW91dCdcbmltcG9ydCB7IFdhbGxldFNlcnZpY2UgfSBmcm9tICdAL2xpYi9zZXJ2aWNlcy93YWxsZXQnXG5pbXBvcnQgeyBEZXBvc2l0UmVxdWVzdCB9IGZyb20gJ0AvdHlwZXMnXG5pbXBvcnQgeyBmb3JtYXRDdXJyZW5jeSB9IGZyb20gJ0AvbGliL3V0aWxzJ1xuaW1wb3J0IHsgc3VwYWJhc2UgfSBmcm9tICdAL2xpYi9zdXBhYmFzZSdcblxuaW50ZXJmYWNlIERlcG9zaXRBY3Rpb25zUHJvcHMge1xuICBkZXBvc2l0OiBEZXBvc2l0UmVxdWVzdFxuICBvbkFwcHJvdmU6IChkZXBvc2l0SWQ6IHN0cmluZykgPT4gdm9pZFxuICBvblJlamVjdDogKGRlcG9zaXRJZDogc3RyaW5nKSA9PiB2b2lkXG59XG5cbmZ1bmN0aW9uIERlcG9zaXRBY3Rpb25zKHsgZGVwb3NpdCwgb25BcHByb3ZlLCBvblJlamVjdCB9OiBEZXBvc2l0QWN0aW9uc1Byb3BzKSB7XG4gIGNvbnN0IFtzaG93TWVudSwgc2V0U2hvd01lbnVdID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICA8YnV0dG9uXG4gICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dNZW51KCFzaG93TWVudSl9XG4gICAgICAgIGNsYXNzTmFtZT1cInAtMiB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS02MDAgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmF5LTEwMFwiXG4gICAgICA+XG4gICAgICAgIDxNb3JlVmVydGljYWwgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICA8L2J1dHRvbj5cbiAgICAgIFxuICAgICAge3Nob3dNZW51ICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSByaWdodC0wIG10LTIgdy00OCBiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1sZyBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHotMTBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB5LTFcIj5cbiAgICAgICAgICAgIHtkZXBvc2l0LnN0YXR1cyA9PT0gJ3BlbmRpbmcnICYmIChcbiAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIG9uQXBwcm92ZShkZXBvc2l0LmlkKVxuICAgICAgICAgICAgICAgICAgICBzZXRTaG93TWVudShmYWxzZSlcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB3LWZ1bGwgcHgtNCBweS0yIHRleHQtc20gdGV4dC1ncmVlbi02MDAgaG92ZXI6YmctZ3JlZW4tNTBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgQXBwcm92ZSBEZXBvc2l0XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBvblJlamVjdChkZXBvc2l0LmlkKVxuICAgICAgICAgICAgICAgICAgICBzZXRTaG93TWVudShmYWxzZSlcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB3LWZ1bGwgcHgtNCBweS0yIHRleHQtc20gdGV4dC1yZWQtNjAwIGhvdmVyOmJnLXJlZC01MFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPFhDaXJjbGUgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgIFJlamVjdCBEZXBvc2l0XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFkbWluRGVwb3NpdHNQYWdlKCkge1xuICBjb25zdCBbZGVwb3NpdHMsIHNldERlcG9zaXRzXSA9IHVzZVN0YXRlPERlcG9zaXRSZXF1ZXN0W10+KFtdKVxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKVxuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtzZWFyY2hUZXJtLCBzZXRTZWFyY2hUZXJtXSA9IHVzZVN0YXRlKCcnKVxuICBjb25zdCBbc2VsZWN0ZWRTdGF0dXMsIHNldFNlbGVjdGVkU3RhdHVzXSA9IHVzZVN0YXRlPHN0cmluZz4oJ2FsbCcpXG4gIGNvbnN0IFtjdXJyZW50UGFnZSwgc2V0Q3VycmVudFBhZ2VdID0gdXNlU3RhdGUoMSlcbiAgY29uc3QgW3RvdGFsRGVwb3NpdHMsIHNldFRvdGFsRGVwb3NpdHNdID0gdXNlU3RhdGUoMClcbiAgY29uc3QgZGVwb3NpdHNQZXJQYWdlID0gMjBcblxuICAvLyBNb2RhbCBzdGF0ZXNcbiAgY29uc3QgW3Nob3dSZWplY3RNb2RhbCwgc2V0U2hvd1JlamVjdE1vZGFsXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbc2hvd01vZGlmeU1vZGFsLCBzZXRTaG93TW9kaWZ5TW9kYWxdID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtzZWxlY3RlZERlcG9zaXQsIHNldFNlbGVjdGVkRGVwb3NpdF0gPSB1c2VTdGF0ZTxEZXBvc2l0UmVxdWVzdCB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtyZWplY3ROb3Rlcywgc2V0UmVqZWN0Tm90ZXNdID0gdXNlU3RhdGUoJycpXG4gIGNvbnN0IFttb2RpZmllZEFtb3VudCwgc2V0TW9kaWZpZWRBbW91bnRdID0gdXNlU3RhdGUoJycpXG4gIGNvbnN0IFthY3Rpb25Mb2FkaW5nLCBzZXRBY3Rpb25Mb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgZmV0Y2hEZXBvc2l0cygpXG4gIH0sIFtjdXJyZW50UGFnZSwgc2VsZWN0ZWRTdGF0dXNdKVxuXG4gIGNvbnN0IGZldGNoRGVwb3NpdHMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSlcbiAgICAgIHNldEVycm9yKG51bGwpXG4gICAgICBjb25zdCBzdGF0dXMgPSBzZWxlY3RlZFN0YXR1cyA9PT0gJ2FsbCcgPyB1bmRlZmluZWQgOiBzZWxlY3RlZFN0YXR1c1xuICAgICAgY29uc3QgeyByZXF1ZXN0cywgdG90YWwgfSA9IGF3YWl0IFdhbGxldFNlcnZpY2UuZ2V0QWxsRGVwb3NpdFJlcXVlc3RzKFxuICAgICAgICBjdXJyZW50UGFnZSxcbiAgICAgICAgZGVwb3NpdHNQZXJQYWdlLFxuICAgICAgICBzdGF0dXNcbiAgICAgIClcbiAgICAgIHNldERlcG9zaXRzKHJlcXVlc3RzIHx8IFtdKVxuICAgICAgc2V0VG90YWxEZXBvc2l0cyh0b3RhbCB8fCAwKVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgZGVwb3NpdHM6JywgZXJyKVxuICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdGYWlsZWQgdG8gbG9hZCBkZXBvc2l0cycpXG4gICAgICBzZXREZXBvc2l0cyhbXSlcbiAgICAgIHNldFRvdGFsRGVwb3NpdHMoMClcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVBcHByb3ZlID0gYXN5bmMgKGRlcG9zaXRJZDogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCFjb25maXJtKCdBcmUgeW91IHN1cmUgeW91IHdhbnQgdG8gYXBwcm92ZSB0aGlzIGRlcG9zaXQ/JykpIHJldHVyblxuXG4gICAgdHJ5IHtcbiAgICAgIHNldEFjdGlvbkxvYWRpbmcodHJ1ZSlcbiAgICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0gfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpXG4gICAgICBpZiAoIXVzZXIpIHRocm93IG5ldyBFcnJvcignTm90IGF1dGhlbnRpY2F0ZWQnKVxuXG4gICAgICBhd2FpdCBXYWxsZXRTZXJ2aWNlLmFwcHJvdmVEZXBvc2l0UmVxdWVzdChkZXBvc2l0SWQsIHVzZXIuaWQpXG4gICAgICBhd2FpdCBmZXRjaERlcG9zaXRzKClcbiAgICAgIGFsZXJ0KCdEZXBvc2l0IGFwcHJvdmVkIHN1Y2Nlc3NmdWxseSEnKVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgYWxlcnQoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdGYWlsZWQgdG8gYXBwcm92ZSBkZXBvc2l0JylcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0QWN0aW9uTG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVSZWplY3QgPSAoZGVwb3NpdElkOiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCBkZXBvc2l0ID0gZGVwb3NpdHMuZmluZChkID0+IGQuaWQgPT09IGRlcG9zaXRJZClcbiAgICBpZiAoIWRlcG9zaXQpIHJldHVyblxuXG4gICAgc2V0U2VsZWN0ZWREZXBvc2l0KGRlcG9zaXQpXG4gICAgc2V0U2hvd1JlamVjdE1vZGFsKHRydWUpXG4gIH1cblxuICBjb25zdCBjb25maXJtUmVqZWN0ID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghc2VsZWN0ZWREZXBvc2l0IHx8ICFyZWplY3ROb3Rlcy50cmltKCkpIHJldHVyblxuXG4gICAgdHJ5IHtcbiAgICAgIHNldEFjdGlvbkxvYWRpbmcodHJ1ZSlcbiAgICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0gfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpXG4gICAgICBpZiAoIXVzZXIpIHRocm93IG5ldyBFcnJvcignTm90IGF1dGhlbnRpY2F0ZWQnKVxuXG4gICAgICBhd2FpdCBXYWxsZXRTZXJ2aWNlLnJlamVjdERlcG9zaXRSZXF1ZXN0KHNlbGVjdGVkRGVwb3NpdC5pZCwgdXNlci5pZCwgcmVqZWN0Tm90ZXMpXG4gICAgICBzZXRTaG93UmVqZWN0TW9kYWwoZmFsc2UpXG4gICAgICBzZXRTZWxlY3RlZERlcG9zaXQobnVsbClcbiAgICAgIHNldFJlamVjdE5vdGVzKCcnKVxuICAgICAgYXdhaXQgZmV0Y2hEZXBvc2l0cygpXG4gICAgICBhbGVydCgnRGVwb3NpdCByZWplY3RlZCBzdWNjZXNzZnVsbHkhJylcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGFsZXJ0KGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnRmFpbGVkIHRvIHJlamVjdCBkZXBvc2l0JylcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0QWN0aW9uTG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRTdGF0dXNDb2xvciA9IChzdGF0dXM6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgICBjYXNlICdhcHByb3ZlZCc6XG4gICAgICAgIHJldHVybiAndGV4dC1ncmVlbi02MDAgYmctZ3JlZW4tMTAwJ1xuICAgICAgY2FzZSAncGVuZGluZyc6XG4gICAgICAgIHJldHVybiAndGV4dC15ZWxsb3ctNjAwIGJnLXllbGxvdy0xMDAnXG4gICAgICBjYXNlICdyZWplY3RlZCc6XG4gICAgICAgIHJldHVybiAndGV4dC1yZWQtNjAwIGJnLXJlZC0xMDAnXG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gJ3RleHQtZ3JheS02MDAgYmctZ3JheS0xMDAnXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZ2V0U3RhdHVzSWNvbiA9IChzdGF0dXM6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgICBjYXNlICdhcHByb3ZlZCc6XG4gICAgICAgIHJldHVybiA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICBjYXNlICdwZW5kaW5nJzpcbiAgICAgICAgcmV0dXJuIDxDbG9jayBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgIGNhc2UgJ3JlamVjdGVkJzpcbiAgICAgICAgcmV0dXJuIDxYQ2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuIDxDbG9jayBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICB9XG4gIH1cblxuICBjb25zdCBmaWx0ZXJlZERlcG9zaXRzID0gZGVwb3NpdHMuZmlsdGVyKGRlcG9zaXQgPT4ge1xuICAgIGNvbnN0IG1hdGNoZXNTZWFyY2ggPSBkZXBvc2l0LnVzZXI/LmZ1bGxfbmFtZT8udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXJtLnRvTG93ZXJDYXNlKCkpIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAgZGVwb3NpdC51c2VyPy5lbWFpbD8udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXJtLnRvTG93ZXJDYXNlKCkpIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAgZGVwb3NpdC5iYW5rX25hbWUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXJtLnRvTG93ZXJDYXNlKCkpIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAgZGVwb3NpdC5hY2NvdW50X2hvbGRlcl9uYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybS50b0xvd2VyQ2FzZSgpKVxuICAgIHJldHVybiBtYXRjaGVzU2VhcmNoXG4gIH0pXG5cbiAgY29uc3QgdG90YWxQYWdlcyA9IE1hdGguY2VpbCh0b3RhbERlcG9zaXRzIC8gZGVwb3NpdHNQZXJQYWdlKVxuXG4gIGNvbnN0IHN0YXR1c0NvdW50cyA9IHtcbiAgICBhbGw6IHRvdGFsRGVwb3NpdHMsXG4gICAgcGVuZGluZzogZGVwb3NpdHMuZmlsdGVyKGQgPT4gZC5zdGF0dXMgPT09ICdwZW5kaW5nJykubGVuZ3RoLFxuICAgIGFwcHJvdmVkOiBkZXBvc2l0cy5maWx0ZXIoZCA9PiBkLnN0YXR1cyA9PT0gJ2FwcHJvdmVkJykubGVuZ3RoLFxuICAgIHJlamVjdGVkOiBkZXBvc2l0cy5maWx0ZXIoZCA9PiBkLnN0YXR1cyA9PT0gJ3JlamVjdGVkJykubGVuZ3RoXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxBZG1pbkxheW91dD5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPkRlcG9zaXQgUmVxdWVzdHM8L2gxPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlJldmlldyBhbmQgbWFuYWdlIHVzZXIgZGVwb3NpdCByZXF1ZXN0czwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiB0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgIDxCYW5rbm90ZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgIDxzcGFuPnt0b3RhbERlcG9zaXRzfSB0b3RhbCByZXF1ZXN0czwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFN0YXRzIENhcmRzICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTQgZ2FwLTRcIj5cbiAgICAgICAgICB7T2JqZWN0LmVudHJpZXMoc3RhdHVzQ291bnRzKS5tYXAoKFtzdGF0dXMsIGNvdW50XSkgPT4gKFxuICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICBrZXk9e3N0YXR1c31cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2VsZWN0ZWRTdGF0dXMoc3RhdHVzKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC00IHJvdW5kZWQtbGcgYm9yZGVyIGN1cnNvci1wb2ludGVyIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgc2VsZWN0ZWRTdGF0dXMgPT09IHN0YXR1c1xuICAgICAgICAgICAgICAgICAgPyAnYm9yZGVyLXByaW1hcnktYmx1ZSBiZy1wcmltYXJ5LWJsdWUvNSdcbiAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci1ncmF5LTIwMCBob3Zlcjpib3JkZXItZ3JheS0zMDAnXG4gICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+e2NvdW50fTwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBjYXBpdGFsaXplXCI+XG4gICAgICAgICAgICAgICAge3N0YXR1cyA9PT0gJ2FsbCcgPyAnVG90YWwgUmVxdWVzdHMnIDogYCR7c3RhdHVzfSBSZXF1ZXN0c2B9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBTZWFyY2ggKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBnYXAtNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgIDxTZWFyY2ggY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0zIHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteS0xLzIgdGV4dC1ncmF5LTQwMCBoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VhcmNoIGJ5IHVzZXIsIGJhbmssIG9yIGFjY291bnQgaG9sZGVyLi4uXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17c2VhcmNoVGVybX1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlYXJjaFRlcm0oZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBwbC0xMCBwci00IHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXByaW1hcnktYmx1ZSBmb2N1czpib3JkZXItdHJhbnNwYXJlbnRcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBEZXBvc2l0cyBUYWJsZSAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLXhsIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgIHtsb2FkaW5nID8gKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBweS0xMlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC04IHctOCBib3JkZXItYi0yIGJvcmRlci1wcmltYXJ5LWJsdWVcIj48L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkgOiBlcnJvciA/IChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgbWItNFwiPntlcnJvcn08L2Rpdj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2ZldGNoRGVwb3NpdHN9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLXByaW1hcnktYmx1ZSB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgaG92ZXI6YmctcHJpbWFyeS1ibHVlLzkwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIFRyeSBBZ2FpblxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LXgtYXV0b1wiPlxuICAgICAgICAgICAgICA8dGFibGUgY2xhc3NOYW1lPVwibWluLXctZnVsbCBkaXZpZGUteSBkaXZpZGUtZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgICA8dGhlYWQgY2xhc3NOYW1lPVwiYmctZ3JheS01MFwiPlxuICAgICAgICAgICAgICAgICAgPHRyPlxuICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgVXNlclxuICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgQW1vdW50XG4gICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICBEZXBvc2l0b3IgJiBSZWZlcmVuY2VcbiAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIFN0YXR1c1xuICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgU3VibWl0dGVkXG4gICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1yaWdodCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgQWN0aW9uc1xuICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgICA8L3RoZWFkPlxuICAgICAgICAgICAgICAgIDx0Ym9keSBjbGFzc05hbWU9XCJiZy13aGl0ZSBkaXZpZGUteSBkaXZpZGUtZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgICAgIHtmaWx0ZXJlZERlcG9zaXRzLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgICAgICAgICAgPHRyPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjb2xTcGFuPXs2fSBjbGFzc05hbWU9XCJweC02IHB5LTEyIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCYW5rbm90ZSBjbGFzc05hbWU9XCJoLTEyIHctMTIgdGV4dC1ncmF5LTQwMCBtYi00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+Tm8gZGVwb3NpdCByZXF1ZXN0cyBmb3VuZDwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c2VhcmNoVGVybSA/ICdUcnkgYWRqdXN0aW5nIHlvdXIgc2VhcmNoIHRlcm1zLicgOiAnTm8gZGVwb3NpdCByZXF1ZXN0cyBoYXZlIGJlZW4gc3VibWl0dGVkIHlldC4nfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgZmlsdGVyZWREZXBvc2l0cy5tYXAoKGRlcG9zaXQpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8dHIga2V5PXtkZXBvc2l0LmlkfSBjbGFzc05hbWU9XCJob3ZlcjpiZy1ncmF5LTUwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMTAgdy0xMCByb3VuZGVkLWZ1bGwgYmctcHJpbWFyeS1ibHVlLzEwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VXNlciBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtcHJpbWFyeS1ibHVlXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtkZXBvc2l0LnVzZXI/LmZ1bGxfbmFtZSB8fCAnTm8gbmFtZSd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+e2RlcG9zaXQudXNlcj8uZW1haWwgfHwgJ05vIGVtYWlsJ308L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0Q3VycmVuY3koZGVwb3NpdC5hbW91bnQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgRGVwb3NpdG9yOiB7ZGVwb3NpdC5kZXBvc2l0b3JfbmFtZSB8fCAnTm90IHNwZWNpZmllZCd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7ZGVwb3NpdC5yZWZlcmVuY2VfbnVtYmVyICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMCBmb250LW1vbm9cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFJlZjoge2RlcG9zaXQucmVmZXJlbmNlX251bWJlcn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2RlcG9zaXQuZGVwb3NpdF9zbGlwX3VybCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPXtkZXBvc2l0LmRlcG9zaXRfc2xpcF91cmx9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhcmdldD1cIl9ibGFua1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlbD1cIm5vb3BlbmVyIG5vcmVmZXJyZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgdGV4dC14cyB0ZXh0LWJsdWUtNjAwIGhvdmVyOnRleHQtYmx1ZS04MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RXllIGNsYXNzTmFtZT1cImgtMyB3LTMgbXItMVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFZpZXcgUHJvb2ZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTIuNSBweS0wLjUgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gJHtnZXRTdGF0dXNDb2xvcihkZXBvc2l0LnN0YXR1cyl9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRTdGF0dXNJY29uKGRlcG9zaXQuc3RhdHVzKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtMSBjYXBpdGFsaXplXCI+e2RlcG9zaXQuc3RhdHVzfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgdGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxDYWxlbmRhciBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7bmV3IERhdGUoZGVwb3NpdC5jcmVhdGVkX2F0KS50b0xvY2FsZURhdGVTdHJpbmcoKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge25ldyBEYXRlKGRlcG9zaXQuY3JlYXRlZF9hdCkudG9Mb2NhbGVUaW1lU3RyaW5nKCl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgdGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPERlcG9zaXRBY3Rpb25zXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGRlcG9zaXQ9e2RlcG9zaXR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQXBwcm92ZT17aGFuZGxlQXBwcm92ZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25SZWplY3Q9e2hhbmRsZVJlamVjdH1cbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgICAgICkpXG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvdGJvZHk+XG4gICAgICAgICAgICAgIDwvdGFibGU+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogUGFnaW5hdGlvbiAqL31cbiAgICAgICAge3RvdGFsUGFnZXMgPiAxICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS03MDBcIj5cbiAgICAgICAgICAgICAgU2hvd2luZyB7KChjdXJyZW50UGFnZSAtIDEpICogZGVwb3NpdHNQZXJQYWdlKSArIDF9IHRvIHtNYXRoLm1pbihjdXJyZW50UGFnZSAqIGRlcG9zaXRzUGVyUGFnZSwgdG90YWxEZXBvc2l0cyl9IG9mIHt0b3RhbERlcG9zaXRzfSByZXF1ZXN0c1xuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0Q3VycmVudFBhZ2UocHJldiA9PiBNYXRoLm1heChwcmV2IC0gMSwgMSkpfVxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtjdXJyZW50UGFnZSA9PT0gMX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGhvdmVyOmJnLWdyYXktNTBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgUHJldmlvdXNcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICB7QXJyYXkuZnJvbSh7IGxlbmd0aDogTWF0aC5taW4oNSwgdG90YWxQYWdlcykgfSwgKF8sIGkpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBwYWdlID0gaSArIE1hdGgubWF4KDEsIGN1cnJlbnRQYWdlIC0gMilcbiAgICAgICAgICAgICAgICByZXR1cm4gcGFnZSA8PSB0b3RhbFBhZ2VzID8gKFxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBrZXk9e3BhZ2V9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEN1cnJlbnRQYWdlKHBhZ2UpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC0zIHB5LTIgYm9yZGVyIHJvdW5kZWQtbGcgJHtcbiAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50UGFnZSA9PT0gcGFnZVxuICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctcHJpbWFyeS1ibHVlIHRleHQtd2hpdGUgYm9yZGVyLXByaW1hcnktYmx1ZSdcbiAgICAgICAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci1ncmF5LTMwMCBob3ZlcjpiZy1ncmF5LTUwJ1xuICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge3BhZ2V9XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICApIDogbnVsbFxuICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRDdXJyZW50UGFnZShwcmV2ID0+IE1hdGgubWluKHByZXYgKyAxLCB0b3RhbFBhZ2VzKSl9XG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2N1cnJlbnRQYWdlID09PSB0b3RhbFBhZ2VzfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgaG92ZXI6YmctZ3JheS01MFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBOZXh0XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFJlamVjdCBNb2RhbCAqL31cbiAgICAgIHtzaG93UmVqZWN0TW9kYWwgJiYgc2VsZWN0ZWREZXBvc2l0ICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIGJnLWJsYWNrIGJnLW9wYWNpdHktNTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgei01MFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBwLTYgdy1mdWxsIG1heC13LW1kXCI+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlJlamVjdCBEZXBvc2l0IFJlcXVlc3Q8L2gzPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00XCI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgWW91IGFyZSBhYm91dCB0byByZWplY3QgYSBkZXBvc2l0IHJlcXVlc3QgZm9yeycgJ31cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntmb3JtYXRDdXJyZW5jeShzZWxlY3RlZERlcG9zaXQuYW1vdW50KX08L3NwYW4+eycgJ31cbiAgICAgICAgICAgICAgICBmcm9tIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e3NlbGVjdGVkRGVwb3NpdC51c2VyPy5mdWxsX25hbWV9PC9zcGFuPi5cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgUmVhc29uIGZvciByZWplY3Rpb24gKlxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgIHZhbHVlPXtyZWplY3ROb3Rlc31cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFJlamVjdE5vdGVzKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wcmltYXJ5LWJsdWUgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgICAgICByb3dzPXs0fVxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiUGxlYXNlIHByb3ZpZGUgYSByZWFzb24gZm9yIHJlamVjdGluZyB0aGlzIGRlcG9zaXQgcmVxdWVzdC4uLlwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgIHNldFNob3dSZWplY3RNb2RhbChmYWxzZSlcbiAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkRGVwb3NpdChudWxsKVxuICAgICAgICAgICAgICAgICAgc2V0UmVqZWN0Tm90ZXMoJycpXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgcHgtNCBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgdGV4dC1ncmF5LTcwMCByb3VuZGVkLWxnIGhvdmVyOmJnLWdyYXktNTBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgQ2FuY2VsXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17Y29uZmlybVJlamVjdH1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17YWN0aW9uTG9hZGluZyB8fCAhcmVqZWN0Tm90ZXMudHJpbSgpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBweC00IHB5LTIgYmctcmVkLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgaG92ZXI6YmctcmVkLTcwMCBkaXNhYmxlZDpvcGFjaXR5LTUwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHthY3Rpb25Mb2FkaW5nID8gJ1JlamVjdGluZy4uLicgOiAnUmVqZWN0IERlcG9zaXQnfVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgPC9BZG1pbkxheW91dD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiQmFua25vdGUiLCJTZWFyY2giLCJNb3JlVmVydGljYWwiLCJDaGVja0NpcmNsZSIsIlhDaXJjbGUiLCJDbG9jayIsIkV5ZSIsIlVzZXIiLCJDYWxlbmRhciIsIkFkbWluTGF5b3V0IiwiV2FsbGV0U2VydmljZSIsImZvcm1hdEN1cnJlbmN5Iiwic3VwYWJhc2UiLCJEZXBvc2l0QWN0aW9ucyIsImRlcG9zaXQiLCJvbkFwcHJvdmUiLCJvblJlamVjdCIsInNob3dNZW51Iiwic2V0U2hvd01lbnUiLCJkaXYiLCJjbGFzc05hbWUiLCJidXR0b24iLCJvbkNsaWNrIiwic3RhdHVzIiwiaWQiLCJBZG1pbkRlcG9zaXRzUGFnZSIsInNlbGVjdGVkRGVwb3NpdCIsImRlcG9zaXRzIiwic2V0RGVwb3NpdHMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJzZWFyY2hUZXJtIiwic2V0U2VhcmNoVGVybSIsInNlbGVjdGVkU3RhdHVzIiwic2V0U2VsZWN0ZWRTdGF0dXMiLCJjdXJyZW50UGFnZSIsInNldEN1cnJlbnRQYWdlIiwidG90YWxEZXBvc2l0cyIsInNldFRvdGFsRGVwb3NpdHMiLCJkZXBvc2l0c1BlclBhZ2UiLCJzaG93UmVqZWN0TW9kYWwiLCJzZXRTaG93UmVqZWN0TW9kYWwiLCJzaG93TW9kaWZ5TW9kYWwiLCJzZXRTaG93TW9kaWZ5TW9kYWwiLCJzZXRTZWxlY3RlZERlcG9zaXQiLCJyZWplY3ROb3RlcyIsInNldFJlamVjdE5vdGVzIiwibW9kaWZpZWRBbW91bnQiLCJzZXRNb2RpZmllZEFtb3VudCIsImFjdGlvbkxvYWRpbmciLCJzZXRBY3Rpb25Mb2FkaW5nIiwiZmV0Y2hEZXBvc2l0cyIsInVuZGVmaW5lZCIsInJlcXVlc3RzIiwidG90YWwiLCJnZXRBbGxEZXBvc2l0UmVxdWVzdHMiLCJlcnIiLCJjb25zb2xlIiwiRXJyb3IiLCJtZXNzYWdlIiwiaGFuZGxlQXBwcm92ZSIsImRlcG9zaXRJZCIsImNvbmZpcm0iLCJkYXRhIiwidXNlciIsImF1dGgiLCJnZXRVc2VyIiwiYXBwcm92ZURlcG9zaXRSZXF1ZXN0IiwiYWxlcnQiLCJoYW5kbGVSZWplY3QiLCJmaW5kIiwiZCIsImNvbmZpcm1SZWplY3QiLCJ0cmltIiwicmVqZWN0RGVwb3NpdFJlcXVlc3QiLCJnZXRTdGF0dXNDb2xvciIsImdldFN0YXR1c0ljb24iLCJmaWx0ZXJlZERlcG9zaXRzIiwiZmlsdGVyIiwibWF0Y2hlc1NlYXJjaCIsImZ1bGxfbmFtZSIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJlbWFpbCIsImJhbmtfbmFtZSIsImFjY291bnRfaG9sZGVyX25hbWUiLCJ0b3RhbFBhZ2VzIiwiTWF0aCIsImNlaWwiLCJzdGF0dXNDb3VudHMiLCJhbGwiLCJwZW5kaW5nIiwibGVuZ3RoIiwiYXBwcm92ZWQiLCJyZWplY3RlZCIsImgxIiwicCIsInNwYW4iLCJPYmplY3QiLCJlbnRyaWVzIiwibWFwIiwiY291bnQiLCJpbnB1dCIsInR5cGUiLCJwbGFjZWhvbGRlciIsInZhbHVlIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0IiwidGFibGUiLCJ0aGVhZCIsInRyIiwidGgiLCJ0Ym9keSIsInRkIiwiY29sU3BhbiIsImgzIiwiYW1vdW50IiwiZGVwb3NpdG9yX25hbWUiLCJyZWZlcmVuY2VfbnVtYmVyIiwiZGVwb3NpdF9zbGlwX3VybCIsImEiLCJocmVmIiwicmVsIiwiRGF0ZSIsImNyZWF0ZWRfYXQiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJ0b0xvY2FsZVRpbWVTdHJpbmciLCJtaW4iLCJwcmV2IiwibWF4IiwiZGlzYWJsZWQiLCJBcnJheSIsImZyb20iLCJfIiwiaSIsInBhZ2UiLCJsYWJlbCIsInRleHRhcmVhIiwicmVxdWlyZWQiLCJyb3dzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/deposits/page.tsx\n"));

/***/ })

});