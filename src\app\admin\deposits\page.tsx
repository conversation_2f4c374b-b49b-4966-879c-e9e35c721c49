'use client'

import { useState, useEffect } from 'react'
import { 
  Banknote, 
  Search, 
  Filter,
  MoreVertical,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  User,
  Calendar,
  DollarSign
} from 'lucide-react'
import AdminLayout from '@/components/admin/AdminLayout'
import { WalletService } from '@/lib/services/wallet'
import { DepositRequest } from '@/types'
import { formatCurrency } from '@/lib/utils'
import { supabase } from '@/lib/supabase'
import { showConfirmation, showAlert } from '@/components/ui/ConfirmationDialog'

interface DepositActionsProps {
  deposit: DepositRequest
  onApprove: (depositId: string) => void
  onReject: (depositId: string) => void
}

function DepositActions({ deposit, onApprove, onReject }: DepositActionsProps) {
  const [showMenu, setShowMenu] = useState(false)

  return (
    <div className="relative">
      <button
        onClick={() => setShowMenu(!showMenu)}
        className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
      >
        <MoreVertical className="h-4 w-4" />
      </button>
      
      {showMenu && (
        <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
          <div className="py-1">
            {deposit.status === 'pending' && (
              <>
                <button
                  onClick={() => {
                    onApprove(deposit.id)
                    setShowMenu(false)
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-green-600 hover:bg-green-50"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Approve Deposit
                </button>
                <button
                  onClick={() => {
                    onReject(deposit.id)
                    setShowMenu(false)
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  Reject Deposit
                </button>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default function AdminDepositsPage() {
  const [deposits, setDeposits] = useState<DepositRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalDeposits, setTotalDeposits] = useState(0)
  const depositsPerPage = 20

  // Modal states
  const [showRejectModal, setShowRejectModal] = useState(false)
  const [showModifyModal, setShowModifyModal] = useState(false)
  const [selectedDeposit, setSelectedDeposit] = useState<DepositRequest | null>(null)
  const [rejectNotes, setRejectNotes] = useState('')
  const [modifiedAmount, setModifiedAmount] = useState('')
  const [actionLoading, setActionLoading] = useState(false)

  useEffect(() => {
    fetchDeposits()
  }, [currentPage, selectedStatus])

  const fetchDeposits = async () => {
    try {
      setLoading(true)
      setError(null)
      const status = selectedStatus === 'all' ? undefined : selectedStatus
      const { requests, total } = await WalletService.getAllDepositRequests(
        currentPage,
        depositsPerPage,
        status
      )
      setDeposits(requests || [])
      setTotalDeposits(total || 0)
    } catch (err) {
      console.error('Error fetching deposits:', err)
      setError(err instanceof Error ? err.message : 'Failed to load deposits')
      setDeposits([])
      setTotalDeposits(0)
    } finally {
      setLoading(false)
    }
  }

  const handleApprove = async (depositId: string) => {
    const confirmed = await showConfirmation({
      title: 'Approve Deposit',
      message: 'Are you sure you want to approve this deposit request?',
      confirmText: 'Approve',
      cancelText: 'Cancel',
      variant: 'success'
    })

    if (!confirmed) return

    try {
      setActionLoading(true)
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('Not authenticated')

      await WalletService.approveDepositRequest(depositId, user.id)
      await fetchDeposits()

      await showAlert({
        title: 'Success',
        message: 'Deposit approved successfully!',
        variant: 'success'
      })
    } catch (err) {
      await showAlert({
        title: 'Error',
        message: err instanceof Error ? err.message : 'Failed to approve deposit',
        variant: 'danger'
      })
    } finally {
      setActionLoading(false)
    }
  }

  const handleReject = (depositId: string) => {
    const deposit = deposits.find(d => d.id === depositId)
    if (!deposit) return

    setSelectedDeposit(deposit)
    setShowRejectModal(true)
  }

  const handleModifyAmount = (depositId: string) => {
    const deposit = deposits.find(d => d.id === depositId)
    if (!deposit) return

    setSelectedDeposit(deposit)
    setModifiedAmount(deposit.amount.toString())
    setShowModifyModal(true)
  }

  const confirmReject = async () => {
    if (!selectedDeposit || !rejectNotes.trim()) return

    try {
      setActionLoading(true)
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('Not authenticated')

      await WalletService.rejectDepositRequest(selectedDeposit.id, user.id, rejectNotes)
      setShowRejectModal(false)
      setSelectedDeposit(null)
      setRejectNotes('')
      await fetchDeposits()
      alert('Deposit rejected successfully!')
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Failed to reject deposit')
    } finally {
      setActionLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'text-green-600 bg-green-100'
      case 'pending':
        return 'text-yellow-600 bg-yellow-100'
      case 'rejected':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-4 w-4" />
      case 'pending':
        return <Clock className="h-4 w-4" />
      case 'rejected':
        return <XCircle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const filteredDeposits = deposits.filter(deposit => {
    const matchesSearch = deposit.user?.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         deposit.user?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         deposit.bank_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         deposit.account_holder_name.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesSearch
  })

  const totalPages = Math.ceil(totalDeposits / depositsPerPage)

  const statusCounts = {
    all: totalDeposits,
    pending: deposits.filter(d => d.status === 'pending').length,
    approved: deposits.filter(d => d.status === 'approved').length,
    rejected: deposits.filter(d => d.status === 'rejected').length
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Deposit Requests</h1>
            <p className="text-gray-600">Review and manage user deposit requests</p>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Banknote className="h-4 w-4" />
            <span>{totalDeposits} total requests</span>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {Object.entries(statusCounts).map(([status, count]) => (
            <div
              key={status}
              onClick={() => setSelectedStatus(status)}
              className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                selectedStatus === status
                  ? 'border-primary-blue bg-primary-blue/5'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="text-2xl font-bold text-gray-900">{count}</div>
              <div className="text-sm text-gray-600 capitalize">
                {status === 'all' ? 'Total Requests' : `${status} Requests`}
              </div>
            </div>
          ))}
        </div>

        {/* Search */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search by user, bank, or account holder..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Deposits Table */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue"></div>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <div className="text-red-600 mb-4">{error}</div>
              <button
                onClick={fetchDeposits}
                className="px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90"
              >
                Try Again
              </button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      User
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Depositor & Reference
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Submitted
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredDeposits.length === 0 ? (
                    <tr>
                      <td colSpan={6} className="px-6 py-12 text-center">
                        <div className="flex flex-col items-center">
                          <Banknote className="h-12 w-12 text-gray-400 mb-4" />
                          <h3 className="text-lg font-medium text-gray-900 mb-2">No deposit requests found</h3>
                          <p className="text-gray-500">
                            {searchTerm ? 'Try adjusting your search terms.' : 'No deposit requests have been submitted yet.'}
                          </p>
                        </div>
                      </td>
                    </tr>
                  ) : (
                    filteredDeposits.map((deposit) => (
                      <tr key={deposit.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4">
                          <div className="flex items-center">
                            <div className="h-10 w-10 rounded-full bg-primary-blue/10 flex items-center justify-center">
                              <User className="h-5 w-5 text-primary-blue" />
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {deposit.user?.full_name || 'No name'}
                              </div>
                              <div className="text-sm text-gray-500">{deposit.user?.email || 'No email'}</div>
                            </div>
                          </div>
                        </td>
                      <td className="px-6 py-4">
                        <div className="text-lg font-bold text-gray-900">
                          {formatCurrency(deposit.amount)}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900">
                          <div className="font-medium">
                            Depositor: {deposit.depositor_name || 'Not specified'}
                          </div>
                          {deposit.reference_number && (
                            <div className="text-xs text-gray-400 font-mono">
                              Ref: {deposit.reference_number}
                            </div>
                          )}
                          {deposit.deposit_slip_url && (
                            <div className="mt-1">
                              <a
                                href={deposit.deposit_slip_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center text-xs text-blue-600 hover:text-blue-800"
                              >
                                <Eye className="h-3 w-3 mr-1" />
                                View Proof
                              </a>
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(deposit.status)}`}>
                          {getStatusIcon(deposit.status)}
                          <span className="ml-1 capitalize">{deposit.status}</span>
                        </span>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500">
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          {new Date(deposit.created_at).toLocaleDateString()}
                        </div>
                        <div className="text-xs text-gray-400">
                          {new Date(deposit.created_at).toLocaleTimeString()}
                        </div>
                      </td>
                      <td className="px-6 py-4 text-right">
                        <DepositActions
                          deposit={deposit}
                          onApprove={handleApprove}
                          onReject={handleReject}
                        />
                      </td>
                    </tr>
                  ))
                  )}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing {((currentPage - 1) * depositsPerPage) + 1} to {Math.min(currentPage * depositsPerPage, totalDeposits)} of {totalDeposits} requests
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Previous
              </button>
              
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = i + Math.max(1, currentPage - 2)
                return page <= totalPages ? (
                  <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={`px-3 py-2 border rounded-lg ${
                      currentPage === page
                        ? 'bg-primary-blue text-white border-primary-blue'
                        : 'border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {page}
                  </button>
                ) : null
              })}
              
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Reject Modal */}
      {showRejectModal && selectedDeposit && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Reject Deposit Request</h3>
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-2">
                You are about to reject a deposit request for{' '}
                <span className="font-medium">{formatCurrency(selectedDeposit.amount)}</span>{' '}
                from <span className="font-medium">{selectedDeposit.user?.full_name}</span>.
              </p>
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Reason for rejection *
              </label>
              <textarea
                required
                value={rejectNotes}
                onChange={(e) => setRejectNotes(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                rows={4}
                placeholder="Please provide a reason for rejecting this deposit request..."
              />
            </div>
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={() => {
                  setShowRejectModal(false)
                  setSelectedDeposit(null)
                  setRejectNotes('')
                }}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={confirmReject}
                disabled={actionLoading || !rejectNotes.trim()}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50"
              >
                {actionLoading ? 'Rejecting...' : 'Reject Deposit'}
              </button>
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  )
}
