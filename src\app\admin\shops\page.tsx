'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { 
  Store, 
  Search, 
  MoreVertical,
  CheckCircle,
  XCircle,
  Clock,
  Star,
  Users,
  Package,
  Trash2,
  Calendar,
  Mail,
  Phone,
  MapPin
} from 'lucide-react'
import AdminLayout from '@/components/admin/AdminLayout'
import { AdminService } from '@/lib/services/admin'
import { VendorShop } from '@/types'
import { showConfirmation } from '@/components/ui/ConfirmationDialog'

interface ShopActionsProps {
  shop: VendorShop
  onStatusUpdate: (shopId: string, status: 'approved' | 'rejected' | 'suspended') => void
  onToggleFeatured: (shopId: string, isFeatured: boolean) => void
  onDelete: (shopId: string) => void
}

function ShopActions({ shop, onStatusUpdate, onToggleFeatured, onDelete }: ShopActionsProps) {
  const [showMenu, setShowMenu] = useState(false)

  return (
    <div className="relative">
      <button
        onClick={() => setShowMenu(!showMenu)}
        className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
      >
        <MoreVertical className="h-4 w-4" />
      </button>
      
      {showMenu && (
        <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
          <div className="py-1">
            {shop.status === 'pending' && (
              <>
                <button
                  onClick={() => {
                    onStatusUpdate(shop.id, 'approved')
                    setShowMenu(false)
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-green-600 hover:bg-green-50"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Approve Shop
                </button>
                <button
                  onClick={() => {
                    onStatusUpdate(shop.id, 'rejected')
                    setShowMenu(false)
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  Reject Shop
                </button>
              </>
            )}
            {shop.status === 'approved' && (
              <>
                <button
                  onClick={() => {
                    onToggleFeatured(shop.id, !shop.is_featured)
                    setShowMenu(false)
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-blue-600 hover:bg-blue-50"
                >
                  <Star className="h-4 w-4 mr-2" />
                  {shop.is_featured ? 'Remove Featured' : 'Make Featured'}
                </button>
                <button
                  onClick={() => {
                    onStatusUpdate(shop.id, 'suspended')
                    setShowMenu(false)
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-orange-600 hover:bg-orange-50"
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  Suspend Shop
                </button>
              </>
            )}
            {shop.status === 'suspended' && (
              <button
                onClick={() => {
                  onStatusUpdate(shop.id, 'approved')
                  setShowMenu(false)
                }}
                className="flex items-center w-full px-4 py-2 text-sm text-green-600 hover:bg-green-50"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Reactivate Shop
              </button>
            )}
            <button
              onClick={async () => {
                const confirmed = await showConfirmation({
                  title: 'Delete Shop',
                  message: 'Are you sure you want to delete this shop? This action cannot be undone.',
                  confirmText: 'Delete',
                  cancelText: 'Cancel',
                  variant: 'danger'
                })

                if (confirmed) {
                  onDelete(shop.id)
                }
                setShowMenu(false)
              }}
              className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Shop
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default function AdminShops() {
  const searchParams = useSearchParams()
  const initialStatus = searchParams.get('status') as 'pending' | 'approved' | 'rejected' | 'suspended' | null

  const [shops, setShops] = useState<VendorShop[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalShops, setTotalShops] = useState(0)
  const [selectedStatus, setSelectedStatus] = useState<string>(initialStatus || 'all')

  const shopsPerPage = 20

  useEffect(() => {
    fetchShops()
  }, [currentPage, selectedStatus])

  const fetchShops = async () => {
    try {
      setLoading(true)
      setError(null)
      const status = selectedStatus === 'all' ? undefined : selectedStatus as any
      const { shops: shopsData, total } = await AdminService.getAllShops(status, currentPage, shopsPerPage)
      setShops(shopsData || [])
      setTotalShops(total || 0)
    } catch (err) {
      console.error('Error fetching shops:', err)
      setError(err instanceof Error ? err.message : 'Failed to load shops')
      setShops([])
      setTotalShops(0)
    } finally {
      setLoading(false)
    }
  }

  const handleStatusUpdate = async (shopId: string, status: 'approved' | 'rejected' | 'suspended') => {
    try {
      await AdminService.updateShopStatus(shopId, status)
      await fetchShops() // Refresh the list
    } catch (err) {
      alert('Failed to update shop status')
    }
  }

  const handleToggleFeatured = async (shopId: string, isFeatured: boolean) => {
    try {
      await AdminService.toggleShopFeatured(shopId, isFeatured)
      await fetchShops() // Refresh the list
    } catch (err) {
      alert('Failed to update featured status')
    }
  }

  const handleDelete = async (shopId: string) => {
    try {
      await AdminService.deleteShop(shopId)
      await fetchShops() // Refresh the list
    } catch (err) {
      alert('Failed to delete shop')
    }
  }

  const filteredShops = shops.filter(shop => 
    shop.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    shop.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    shop.user?.full_name?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const totalPages = Math.ceil(totalShops / shopsPerPage)

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-orange-100 text-orange-800'
      case 'rejected': return 'bg-red-100 text-red-800'
      case 'suspended': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return CheckCircle
      case 'pending': return Clock
      case 'rejected': return XCircle
      case 'suspended': return XCircle
      default: return Store
    }
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-300 rounded w-1/4 mb-6"></div>
          <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="h-10 bg-gray-300 rounded w-1/3"></div>
            </div>
            <div className="divide-y divide-gray-200">
              {Array.from({ length: 10 }).map((_, i) => (
                <div key={i} className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="h-16 w-16 bg-gray-300 rounded-lg"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-300 rounded w-1/3 mb-2"></div>
                      <div className="h-3 bg-gray-300 rounded w-1/2 mb-2"></div>
                      <div className="h-3 bg-gray-300 rounded w-1/4"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Vendor Shops Management</h1>
            <p className="text-gray-600">Review and manage vendor shops</p>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Store className="h-4 w-4" />
            <span>{totalShops} total shops</span>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search shops by name, description, or owner..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={selectedStatus}
                onChange={(e) => {
                  setSelectedStatus(e.target.value)
                  setCurrentPage(1)
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
                <option value="suspended">Suspended</option>
              </select>
            </div>
          </div>
        </div>

        {/* Shops List */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          {error ? (
            <div className="text-center py-12">
              <div className="text-red-600 mb-4">{error}</div>
              <button
                onClick={fetchShops}
                className="px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90"
              >
                Try Again
              </button>
            </div>
          ) : filteredShops.length === 0 ? (
            <div className="text-center py-12">
              <div className="flex flex-col items-center">
                <Store className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No shops found</h3>
                <p className="text-gray-500">
                  {searchTerm ? 'Try adjusting your search terms.' : 'No vendor shops have been created yet.'}
                </p>
              </div>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {filteredShops.map((shop) => {
              const StatusIcon = getStatusIcon(shop.status)
              return (
                <div key={shop.id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-start space-x-4">
                    {/* Shop Logo */}
                    <div className="h-16 w-16 bg-gray-200 rounded-lg flex-shrink-0 overflow-hidden">
                      {shop.logo_url ? (
                        <img
                          src={shop.logo_url}
                          alt={shop.name}
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <div className="h-full w-full flex items-center justify-center">
                          <Store className="h-6 w-6 text-gray-400" />
                        </div>
                      )}
                    </div>

                    {/* Shop Details */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h3 className="text-lg font-medium text-gray-900 truncate">
                              {shop.name}
                            </h3>
                            {shop.is_featured && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <Star className="h-3 w-3 mr-1" />
                                Featured
                              </span>
                            )}
                          </div>
                          
                          {shop.description && (
                            <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                              {shop.description}
                            </p>
                          )}
                          
                          <div className="flex items-center space-x-4 text-sm text-gray-500 mb-2">
                            <div className="flex items-center">
                              <Star className="h-3 w-3 mr-1" />
                              {shop.rating.toFixed(1)} ({shop.total_reviews} reviews)
                            </div>
                            <div className="flex items-center">
                              <Package className="h-3 w-3 mr-1" />
                              {shop.total_products} products
                            </div>
                            <div className="flex items-center">
                              <Calendar className="h-3 w-3 mr-1" />
                              {new Date(shop.created_at).toLocaleDateString()}
                            </div>
                          </div>

                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">
                              Owner: {shop.user?.full_name || 'Unknown'}
                            </span>
                            {shop.contact_email && (
                              <>
                                <span className="text-gray-300">•</span>
                                <div className="flex items-center text-sm text-gray-600">
                                  <Mail className="h-3 w-3 mr-1" />
                                  {shop.contact_email}
                                </div>
                              </>
                            )}
                            {shop.contact_phone && (
                              <>
                                <span className="text-gray-300">•</span>
                                <div className="flex items-center text-sm text-gray-600">
                                  <Phone className="h-3 w-3 mr-1" />
                                  {shop.contact_phone}
                                </div>
                              </>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center space-x-3 ml-4">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(shop.status)}`}>
                            <StatusIcon className="h-3 w-3 mr-1" />
                            {shop.status.charAt(0).toUpperCase() + shop.status.slice(1)}
                          </span>
                          <ShopActions
                            shop={shop}
                            onStatusUpdate={handleStatusUpdate}
                            onToggleFeatured={handleToggleFeatured}
                            onDelete={handleDelete}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
            </div>
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing {((currentPage - 1) * shopsPerPage) + 1} to {Math.min(currentPage * shopsPerPage, totalShops)} of {totalShops} shops
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
