const fs = require('fs')
const path = require('path')

function showMigration(migrationFile) {
  console.log(`\n${'='.repeat(80)}`)
  console.log(`MIGRATION: ${migrationFile}`)
  console.log(`${'='.repeat(80)}`)
  
  try {
    const migrationPath = path.join('supabase', 'migrations', migrationFile)
    const sql = fs.readFileSync(migrationPath, 'utf8')
    console.log(sql)
  } catch (error) {
    console.error(`Error reading ${migrationFile}:`, error.message)
  }
}

console.log('📋 SQL MIGRATIONS TO APPLY IN SUPABASE SQL EDITOR')
console.log('Copy and paste each migration below into the Supabase SQL Editor:')

// Show migrations in order
const migrations = [
  '012_update_deposit_requests.sql',
  '013_add_transaction_reference_numbers.sql', 
  '014_update_wallet_functions_with_reference.sql'
]

for (const migration of migrations) {
  showMigration(migration)
}

console.log(`\n${'='.repeat(80)}`)
console.log('✅ All migrations shown above. Apply them in order in Supabase SQL Editor.')
console.log(`${'='.repeat(80)}`)
