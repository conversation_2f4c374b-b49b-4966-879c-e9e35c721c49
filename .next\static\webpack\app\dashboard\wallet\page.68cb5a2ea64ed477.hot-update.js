"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/wallet/page",{

/***/ "(app-pages-browser)/./src/lib/services/wallet.ts":
/*!************************************!*\
  !*** ./src/lib/services/wallet.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletService: function() { return /* binding */ WalletService; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n/**\n * WalletService - Completely rebuilt to handle Supabase relationships correctly\n *\n * Key fixes:\n * - Uses correct foreign key constraint names from migration files\n * - Robust error handling for all operations\n * - Proper null/empty state handling\n * - Comprehensive logging for debugging\n */ class WalletService {\n    /**\n   * Get user's wallet\n   */ static async getUserWallet(userId) {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USER_WALLETS).select(\"*\").eq(\"user_id\", userId).single();\n        if (error) {\n            if (error.code === \"PGRST116\") {\n                // No wallet found, create one\n                return await this.createUserWallet(userId);\n            }\n            throw new Error(\"Failed to fetch wallet: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Create user wallet\n   */ static async createUserWallet(userId) {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USER_WALLETS).insert({\n            user_id: userId,\n            balance: 0.00,\n            currency: \"LKR\"\n        }).select().single();\n        if (error) {\n            throw new Error(\"Failed to create wallet: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Get wallet transactions with pagination\n   */ static async getWalletTransactions(userId) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, limit = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 20, filters = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : {};\n        const offset = (page - 1) * limit;\n        // First get the user's wallet\n        const wallet = await this.getUserWallet(userId);\n        if (!wallet) {\n            return {\n                transactions: [],\n                total: 0\n            };\n        }\n        let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.WALLET_TRANSACTIONS).select(\"*\", {\n            count: \"exact\"\n        }).eq(\"wallet_id\", wallet.id).order(\"created_at\", {\n            ascending: false\n        });\n        // Apply filters\n        if (filters.transaction_type) {\n            query = query.eq(\"transaction_type\", filters.transaction_type);\n        }\n        if (filters.status) {\n            query = query.eq(\"status\", filters.status);\n        }\n        if (filters.date_from) {\n            query = query.gte(\"created_at\", filters.date_from);\n        }\n        if (filters.date_to) {\n            query = query.lte(\"created_at\", filters.date_to);\n        }\n        const { data, error, count } = await query.range(offset, offset + limit - 1);\n        if (error) {\n            throw new Error(\"Failed to fetch transactions: \".concat(error.message));\n        }\n        return {\n            transactions: data || [],\n            total: count || 0\n        };\n    }\n    /**\n   * Create P2P transfer\n   */ static async createP2PTransfer(senderId, receiverEmail, amount, description) {\n        // First, find the receiver by email\n        const { data: receiver, error: receiverError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"id, email, full_name\").eq(\"email\", receiverEmail).single();\n        if (receiverError || !receiver) {\n            throw new Error(\"Receiver not found\");\n        }\n        if (receiver.id === senderId) {\n            throw new Error(\"Cannot transfer to yourself\");\n        }\n        // Check sender's balance\n        const senderWallet = await this.getUserWallet(senderId);\n        if (!senderWallet || senderWallet.balance < amount) {\n            throw new Error(\"Insufficient balance\");\n        }\n        // Call the database function to process the transfer\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"process_p2p_transfer\", {\n            p_sender_id: senderId,\n            p_receiver_id: receiver.id,\n            p_amount: amount,\n            p_description: description\n        });\n        if (error) {\n            throw new Error(\"Transfer failed: \".concat(error.message));\n        }\n        // Fetch the created transfer with proper error handling\n        console.log(\"Fetching created transfer with ID: \".concat(data));\n        const { data: transfer, error: transferError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.P2P_TRANSFERS).select(\"\\n        *,\\n        sender:users!p2p_transfers_sender_id_fkey(id, full_name, email),\\n        receiver:users!p2p_transfers_receiver_id_fkey(id, full_name, email)\\n      \").eq(\"id\", data).single();\n        if (transferError) {\n            console.error(\"Error fetching transfer details:\", transferError);\n            throw new Error(\"Failed to fetch transfer details: \".concat(transferError.message));\n        }\n        if (!transfer) {\n            throw new Error(\"Transfer was created but could not be retrieved\");\n        }\n        console.log(\"Transfer created successfully:\", transfer.id);\n        return transfer;\n    }\n    /**\n   * Get user's P2P transfers with proper error handling\n   */ static async getUserTransfers(userId) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, limit = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 20;\n        try {\n            if (!userId) {\n                throw new Error(\"User ID is required\");\n            }\n            const offset = (page - 1) * limit;\n            console.log(\"Fetching P2P transfers for user: \".concat(userId, \", page: \").concat(page, \", limit: \").concat(limit));\n            const { data, error, count } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.P2P_TRANSFERS).select(\"\\n          *,\\n          sender:users!p2p_transfers_sender_id_fkey(id, full_name, email),\\n          receiver:users!p2p_transfers_receiver_id_fkey(id, full_name, email)\\n        \", {\n                count: \"exact\"\n            }).or(\"sender_id.eq.\".concat(userId, \",receiver_id.eq.\").concat(userId)).order(\"created_at\", {\n                ascending: false\n            }).range(offset, offset + limit - 1);\n            if (error) {\n                console.error(\"Error fetching P2P transfers:\", error);\n                throw new Error(\"Failed to fetch transfers: \".concat(error.message));\n            }\n            console.log(\"Successfully fetched \".concat((data === null || data === void 0 ? void 0 : data.length) || 0, \" transfers\"));\n            return {\n                transfers: data || [],\n                total: count || 0\n            };\n        } catch (error) {\n            console.error(\"WalletService.getUserTransfers error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create deposit request with new structure and reference number\n   */ static async createDepositRequest(userId, depositData) {\n        const referenceNumber = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.generateTransactionReference)();\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.DEPOSIT_REQUESTS).insert({\n            user_id: userId,\n            amount: depositData.amount,\n            currency: \"LKR\",\n            depositor_name: depositData.depositor_name,\n            deposit_slip_url: depositData.deposit_slip_url,\n            notes: depositData.notes,\n            reference_number: referenceNumber,\n            status: \"pending\"\n        }).select().single();\n        if (error) {\n            throw new Error(\"Failed to create deposit request: \".concat(error.message));\n        }\n        return data;\n    }\n    /**\n   * Get user's deposit requests with comprehensive error handling\n   */ static async getUserDepositRequests(userId) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, limit = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 20;\n        try {\n            if (!userId) {\n                throw new Error(\"User ID is required\");\n            }\n            const offset = (page - 1) * limit;\n            console.log(\"Fetching deposit requests for user: \".concat(userId, \", page: \").concat(page, \", limit: \").concat(limit));\n            const { data, error, count } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.DEPOSIT_REQUESTS).select(\"\\n          *,\\n          user:users!deposit_requests_user_id_fkey(id, full_name, email)\\n        \", {\n                count: \"exact\"\n            }).eq(\"user_id\", userId).order(\"created_at\", {\n                ascending: false\n            }).range(offset, offset + limit - 1);\n            if (error) {\n                console.error(\"Error fetching deposit requests:\", error);\n                throw new Error(\"Failed to fetch deposit requests: \".concat(error.message));\n            }\n            console.log(\"Successfully fetched \".concat((data === null || data === void 0 ? void 0 : data.length) || 0, \" deposit requests\"));\n            return {\n                requests: data || [],\n                total: count || 0\n            };\n        } catch (error) {\n            console.error(\"WalletService.getUserDepositRequests error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all deposit requests (admin only) with comprehensive error handling\n   */ static async getAllDepositRequests() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20, status = arguments.length > 2 ? arguments[2] : void 0;\n        try {\n            const offset = (page - 1) * limit;\n            console.log(\"Fetching all deposit requests - page: \".concat(page, \", limit: \").concat(limit, \", status: \").concat(status || \"all\"));\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.DEPOSIT_REQUESTS).select(\"\\n          *,\\n          user:users!deposit_requests_user_id_fkey(id, full_name, email)\\n        \", {\n                count: \"exact\"\n            }).order(\"created_at\", {\n                ascending: false\n            });\n            if (status && status !== \"all\") {\n                query = query.eq(\"status\", status);\n            }\n            const { data, error, count } = await query.range(offset, offset + limit - 1);\n            if (error) {\n                console.error(\"Error fetching all deposit requests:\", error);\n                throw new Error(\"Failed to fetch deposit requests: \".concat(error.message));\n            }\n            console.log(\"Successfully fetched \".concat((data === null || data === void 0 ? void 0 : data.length) || 0, \" deposit requests (total: \").concat(count || 0, \")\"));\n            return {\n                requests: data || [],\n                total: count || 0\n            };\n        } catch (error) {\n            console.error(\"WalletService.getAllDepositRequests error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create a new deposit request with comprehensive validation\n   */ static async createDepositRequest(userId, requestData) {\n        try {\n            var _requestData_bank_name, _requestData_account_holder_name, _requestData_account_number, _requestData_transaction_reference, _requestData_notes;\n            if (!userId) {\n                throw new Error(\"User ID is required\");\n            }\n            if (!requestData.amount || requestData.amount <= 0) {\n                throw new Error(\"Valid amount is required\");\n            }\n            if (!((_requestData_bank_name = requestData.bank_name) === null || _requestData_bank_name === void 0 ? void 0 : _requestData_bank_name.trim())) {\n                throw new Error(\"Bank name is required\");\n            }\n            if (!((_requestData_account_holder_name = requestData.account_holder_name) === null || _requestData_account_holder_name === void 0 ? void 0 : _requestData_account_holder_name.trim())) {\n                throw new Error(\"Account holder name is required\");\n            }\n            if (!((_requestData_account_number = requestData.account_number) === null || _requestData_account_number === void 0 ? void 0 : _requestData_account_number.trim())) {\n                throw new Error(\"Account number is required\");\n            }\n            console.log(\"Creating deposit request for user: \".concat(userId, \", amount: \").concat(requestData.amount));\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.DEPOSIT_REQUESTS).insert({\n                user_id: userId,\n                amount: requestData.amount,\n                currency: \"LKR\",\n                bank_name: requestData.bank_name.trim(),\n                account_holder_name: requestData.account_holder_name.trim(),\n                account_number: requestData.account_number.trim(),\n                transaction_reference: ((_requestData_transaction_reference = requestData.transaction_reference) === null || _requestData_transaction_reference === void 0 ? void 0 : _requestData_transaction_reference.trim()) || null,\n                notes: ((_requestData_notes = requestData.notes) === null || _requestData_notes === void 0 ? void 0 : _requestData_notes.trim()) || null,\n                status: \"pending\"\n            }).select(\"\\n          *,\\n          user:users!deposit_requests_user_id_fkey(id, full_name, email)\\n        \").single();\n            if (error) {\n                console.error(\"Error creating deposit request:\", error);\n                throw new Error(\"Failed to create deposit request: \".concat(error.message));\n            }\n            if (!data) {\n                throw new Error(\"Deposit request was not created\");\n            }\n            console.log(\"Deposit request created successfully:\", data.id);\n            return data;\n        } catch (error) {\n            console.error(\"WalletService.createDepositRequest error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Approve deposit request (admin only) with comprehensive error handling\n   */ static async approveDepositRequest(requestId, adminId, adminNotes) {\n        try {\n            if (!requestId) {\n                throw new Error(\"Request ID is required\");\n            }\n            if (!adminId) {\n                throw new Error(\"Admin ID is required\");\n            }\n            console.log(\"Approving deposit request: \".concat(requestId, \" by admin: \").concat(adminId));\n            // Get the deposit request\n            const { data: request, error: requestError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.DEPOSIT_REQUESTS).select(\"*\").eq(\"id\", requestId).single();\n            if (requestError) {\n                console.error(\"Error fetching deposit request:\", requestError);\n                throw new Error(\"Deposit request not found: \".concat(requestError.message));\n            }\n            if (!request) {\n                throw new Error(\"Deposit request not found\");\n            }\n            if (request.status !== \"pending\") {\n                throw new Error(\"Deposit request is \".concat(request.status, \", not pending\"));\n            }\n            // Get user's wallet\n            const wallet = await this.getUserWallet(request.user_id);\n            if (!wallet) {\n                throw new Error(\"User wallet not found\");\n            }\n            // Update wallet balance using the database function\n            const { data: transactionId, error: balanceError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"update_wallet_balance\", {\n                p_wallet_id: wallet.id,\n                p_amount: request.amount,\n                p_transaction_type: \"deposit\",\n                p_description: \"Bank deposit - \".concat(request.bank_name),\n                p_reference_id: requestId,\n                p_reference_type: \"deposit_request\"\n            });\n            if (balanceError) {\n                console.error(\"Error updating wallet balance:\", balanceError);\n                throw new Error(\"Failed to update wallet balance: \".concat(balanceError.message));\n            }\n            // Update deposit request status\n            const { error: updateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.DEPOSIT_REQUESTS).update({\n                status: \"approved\",\n                approved_by: adminId,\n                approved_at: new Date().toISOString(),\n                admin_notes: (adminNotes === null || adminNotes === void 0 ? void 0 : adminNotes.trim()) || null,\n                wallet_transaction_id: transactionId,\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", requestId);\n            if (updateError) {\n                console.error(\"Error updating deposit request:\", updateError);\n                throw new Error(\"Failed to approve deposit request: \".concat(updateError.message));\n            }\n            console.log(\"Deposit request \".concat(requestId, \" approved successfully\"));\n        } catch (error) {\n            console.error(\"WalletService.approveDepositRequest error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Reject deposit request (admin only) with comprehensive error handling\n   */ static async rejectDepositRequest(requestId, adminId, adminNotes) {\n        try {\n            if (!requestId) {\n                throw new Error(\"Request ID is required\");\n            }\n            if (!adminId) {\n                throw new Error(\"Admin ID is required\");\n            }\n            if (!(adminNotes === null || adminNotes === void 0 ? void 0 : adminNotes.trim())) {\n                throw new Error(\"Admin notes are required for rejection\");\n            }\n            console.log(\"Rejecting deposit request: \".concat(requestId, \" by admin: \").concat(adminId));\n            // First check if the request exists and is pending\n            const { data: request, error: fetchError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.DEPOSIT_REQUESTS).select(\"id, status\").eq(\"id\", requestId).single();\n            if (fetchError) {\n                console.error(\"Error fetching deposit request:\", fetchError);\n                throw new Error(\"Deposit request not found: \".concat(fetchError.message));\n            }\n            if (!request) {\n                throw new Error(\"Deposit request not found\");\n            }\n            if (request.status !== \"pending\") {\n                throw new Error(\"Deposit request is \".concat(request.status, \", not pending\"));\n            }\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.DEPOSIT_REQUESTS).update({\n                status: \"rejected\",\n                approved_by: adminId,\n                approved_at: new Date().toISOString(),\n                admin_notes: adminNotes.trim(),\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", requestId);\n            if (error) {\n                console.error(\"Error rejecting deposit request:\", error);\n                throw new Error(\"Failed to reject deposit request: \".concat(error.message));\n            }\n            console.log(\"Deposit request \".concat(requestId, \" rejected successfully\"));\n        } catch (error) {\n            console.error(\"WalletService.rejectDepositRequest error:\", error);\n            throw error;\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/wallet.ts\n"));

/***/ })

});